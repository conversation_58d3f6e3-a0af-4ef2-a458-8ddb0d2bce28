"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/scheduler/calendar/page",{

/***/ "(app-pages-browser)/./src/hooks/useSocket.js":
/*!********************************!*\
  !*** ./src/hooks/useSocket.js ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\nvar _s = $RefreshSig$();\n\n\n// Função auxiliar para obter o token atual\nconst getCurrentToken = ()=>{\n    if (true) {\n        return localStorage.getItem('token');\n    }\n    return null;\n};\nfunction useSocket(url) {\n    _s();\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSocket.useEffect\": ()=>{\n            // Verificar se o usuário está autenticado\n            const token = getCurrentToken();\n            if (!token) return;\n            // Criar instância do socket\n            const socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_1__.io)(url, {\n                auth: {\n                    token\n                },\n                reconnectionAttempts: 5,\n                reconnectionDelay: 1000,\n                autoConnect: true\n            });\n            // Configurar handlers de conexão\n            socketInstance.on('connect', {\n                \"useSocket.useEffect\": ()=>{\n                    console.log('Socket conectado:', socketInstance.id);\n                }\n            }[\"useSocket.useEffect\"]);\n            socketInstance.on('connect_error', {\n                \"useSocket.useEffect\": (err)=>{\n                    console.error('Erro de conexão:', err.message);\n                }\n            }[\"useSocket.useEffect\"]);\n            socketInstance.on('disconnect', {\n                \"useSocket.useEffect\": (reason)=>{\n                    console.log('Socket desconectado:', reason);\n                }\n            }[\"useSocket.useEffect\"]);\n            setSocket(socketInstance);\n            // Limpar socket ao desmontar\n            return ({\n                \"useSocket.useEffect\": ()=>{\n                    if (socketInstance) {\n                        socketInstance.disconnect();\n                    }\n                }\n            })[\"useSocket.useEffect\"];\n        }\n    }[\"useSocket.useEffect\"], [\n        url\n    ]);\n    return socket;\n}\n_s(useSocket, \"NvwYO9vJOwIMt5STdlMKfWhuxZw=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSocket.js\n"));

/***/ })

});