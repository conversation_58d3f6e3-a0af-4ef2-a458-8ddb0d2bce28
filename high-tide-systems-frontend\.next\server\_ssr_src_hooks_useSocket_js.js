"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_hooks_useSocket_js";
exports.ids = ["_ssr_src_hooks_useSocket_js"];
exports.modules = {

/***/ "(ssr)/./src/hooks/useSocket.js":
/*!********************************!*\
  !*** ./src/hooks/useSocket.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* __next_internal_client_entry_do_not_use__ useSocket auto */ \n\n// Função auxiliar para obter o token atual\nconst getCurrentToken = ()=>{\n    if (false) {}\n    return null;\n};\nfunction useSocket(url) {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSocket.useEffect\": ()=>{\n            // Verificar se estamos no lado do cliente\n            if (true) return;\n            // Verificar se o usuário está autenticado\n            const token = getCurrentToken();\n            if (!token) return;\n            // Criar instância do socket\n            const socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(url, {\n                auth: {\n                    token\n                },\n                reconnectionAttempts: 5,\n                reconnectionDelay: 1000,\n                autoConnect: true\n            });\n            // Configurar handlers de conexão\n            socketInstance.on('connect', {\n                \"useSocket.useEffect\": ()=>{\n                    console.log('[SOCKET] Conectado:', socketInstance.id);\n                }\n            }[\"useSocket.useEffect\"]);\n            socketInstance.on('connect_error', {\n                \"useSocket.useEffect\": (err)=>{\n                    console.error('[SOCKET] Erro de conexão:', err.message);\n                }\n            }[\"useSocket.useEffect\"]);\n            socketInstance.on('disconnect', {\n                \"useSocket.useEffect\": (reason)=>{\n                    console.log('[SOCKET] Desconectado:', reason);\n                }\n            }[\"useSocket.useEffect\"]);\n            setSocket(socketInstance);\n            // Limpar socket ao desmontar\n            return ({\n                \"useSocket.useEffect\": ()=>{\n                    if (socketInstance) {\n                        socketInstance.disconnect();\n                    }\n                }\n            })[\"useSocket.useEffect\"];\n        }\n    }[\"useSocket.useEffect\"], [\n        url\n    ]);\n    return socket;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useSocket.js\n");

/***/ })

};
;