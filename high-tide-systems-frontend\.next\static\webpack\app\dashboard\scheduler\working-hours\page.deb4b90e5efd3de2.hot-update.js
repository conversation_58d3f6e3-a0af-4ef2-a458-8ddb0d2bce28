"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/scheduler/working-hours/page",{

/***/ "(app-pages-browser)/./src/app/modules/scheduler/calendar/hooks/useAppointmentCalendar.js":
/*!****************************************************************************!*\
  !*** ./src/app/modules/scheduler/calendar/hooks/useAppointmentCalendar.js ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/modules/scheduler/services/appointmentService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/appointmentService.js\");\n/* harmony import */ var _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/appointmentConstants */ \"(app-pages-browser)/./src/app/modules/scheduler/calendar/utils/appointmentConstants.js\");\n/* harmony import */ var _utils_dateFormatters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/dateFormatters */ \"(app-pages-browser)/./src/utils/dateFormatters.js\");\n/* harmony import */ var _hooks_useSocket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSocket */ \"(app-pages-browser)/./src/hooks/useSocket.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\n\n\nconst useAppointmentCalendar = (filters, isDarkMode, permissions)=>{\n    _s();\n    const calendarRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [selectedAppointment, setSelectedAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"dayGridMonth\");\n    // Extrair permissões\n    const { canCreateAppointment, canEditAppointment, canDeleteAppointment } = permissions;\n    // Socket.IO para atualizações em tempo real\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Inicializar Socket.IO apenas no cliente\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAppointmentCalendar.useEffect\": ()=>{\n            if (false) {}\n            const initSocket = {\n                \"useAppointmentCalendar.useEffect.initSocket\": async ()=>{\n                    try {\n                        const token = localStorage.getItem('token');\n                        if (!token) return;\n                        const { useSocket } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/hooks/useSocket */ \"(app-pages-browser)/./src/hooks/useSocket.js\"));\n                        const socketInstance = useSocket(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000');\n                        setSocket(socketInstance);\n                    } catch (error) {\n                        console.error('[CALENDAR-SOCKET] Erro ao inicializar socket:', error);\n                    }\n                }\n            }[\"useAppointmentCalendar.useEffect.initSocket\"];\n            initSocket();\n        }\n    }[\"useAppointmentCalendar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAppointmentCalendar.useEffect\": ()=>{\n            loadAppointments();\n        }\n    }[\"useAppointmentCalendar.useEffect\"], []);\n    // Socket.IO listeners para atualizações em tempo real\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAppointmentCalendar.useEffect\": ()=>{\n            if (!socket || typeof socket.on !== 'function') {\n                console.log('[CALENDAR-SOCKET] Socket não disponível ou inválido');\n                return;\n            }\n            console.log('[CALENDAR-SOCKET] Configurando listeners Socket.IO');\n            // Listener para agendamentos criados\n            const handleAppointmentCreated = {\n                \"useAppointmentCalendar.useEffect.handleAppointmentCreated\": (data)=>{\n                    console.log('[CALENDAR-SOCKET] Agendamento(s) criado(s):', data);\n                    // Recarregar agendamentos para garantir que temos os dados mais atuais\n                    loadAppointments();\n                }\n            }[\"useAppointmentCalendar.useEffect.handleAppointmentCreated\"];\n            // Listener para agendamentos atualizados\n            const handleAppointmentUpdated = {\n                \"useAppointmentCalendar.useEffect.handleAppointmentUpdated\": (data)=>{\n                    console.log('[CALENDAR-SOCKET] Agendamento atualizado:', data);\n                    // Recarregar agendamentos para garantir que temos os dados mais atuais\n                    loadAppointments();\n                }\n            }[\"useAppointmentCalendar.useEffect.handleAppointmentUpdated\"];\n            // Listener para agendamentos deletados\n            const handleAppointmentDeleted = {\n                \"useAppointmentCalendar.useEffect.handleAppointmentDeleted\": (data)=>{\n                    console.log('[CALENDAR-SOCKET] Agendamento deletado:', data);\n                    // Recarregar agendamentos para garantir que temos os dados mais atuais\n                    loadAppointments();\n                }\n            }[\"useAppointmentCalendar.useEffect.handleAppointmentDeleted\"];\n            // Registrar listeners\n            socket.on('appointment:created', handleAppointmentCreated);\n            socket.on('appointment:updated', handleAppointmentUpdated);\n            socket.on('appointment:deleted', handleAppointmentDeleted);\n            // Cleanup listeners\n            return ({\n                \"useAppointmentCalendar.useEffect\": ()=>{\n                    console.log('[CALENDAR-SOCKET] Removendo listeners Socket.IO');\n                    if (socket && typeof socket.off === 'function') {\n                        socket.off('appointment:created', handleAppointmentCreated);\n                        socket.off('appointment:updated', handleAppointmentUpdated);\n                        socket.off('appointment:deleted', handleAppointmentDeleted);\n                    }\n                }\n            })[\"useAppointmentCalendar.useEffect\"];\n        }\n    }[\"useAppointmentCalendar.useEffect\"], [\n        socket\n    ]);\n    // Recarregar os agendamentos quando o modo dark muda para ajustar as cores\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAppointmentCalendar.useEffect\": ()=>{\n            if (appointments.length > 0) {\n                const updatedAppointments = appointments.map({\n                    \"useAppointmentCalendar.useEffect.updatedAppointments\": (appointment)=>{\n                        var _APPOINTMENT_STATUS_, _APPOINTMENT_STATUS_1, _APPOINTMENT_STATUS_2, _APPOINTMENT_STATUS_3;\n                        return {\n                            ...appointment,\n                            backgroundColor: isDarkMode ? (_APPOINTMENT_STATUS_ = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_ === void 0 ? void 0 : _APPOINTMENT_STATUS_.darkColor : (_APPOINTMENT_STATUS_1 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_1 === void 0 ? void 0 : _APPOINTMENT_STATUS_1.color,\n                            borderColor: isDarkMode ? (_APPOINTMENT_STATUS_2 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_2 === void 0 ? void 0 : _APPOINTMENT_STATUS_2.darkColor : (_APPOINTMENT_STATUS_3 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_3 === void 0 ? void 0 : _APPOINTMENT_STATUS_3.color\n                        };\n                    }\n                }[\"useAppointmentCalendar.useEffect.updatedAppointments\"]);\n                setAppointments(updatedAppointments);\n            }\n        }\n    }[\"useAppointmentCalendar.useEffect\"], [\n        isDarkMode\n    ]);\n    const loadAppointments = async function() {\n        let searchFilters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : filters;\n        setIsLoading(true);\n        try {\n            // Para clientes, não enviamos o filtro de providers (profissionais)\n            const clientSafeFilters = {\n                ...searchFilters\n            };\n            // Se o usuário for cliente, remover o filtro de providers\n            if (clientSafeFilters.providers && clientSafeFilters.providers.length > 0) {\n                console.log(\"[CLIENT-FILTER] Removendo filtro de providers para cliente\");\n                delete clientSafeFilters.providers;\n            }\n            const response = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_1__.appointmentService.getAppointments(clientSafeFilters);\n            if (!(response === null || response === void 0 ? void 0 : response.schedulings) && !(response === null || response === void 0 ? void 0 : response.appointments)) {\n                console.error(\"Resposta inválida da API:\", response);\n                setAppointments([]);\n                return;\n            }\n            const schedulings = response.appointments || [];\n            console.log(\"[APPOINTMENTS] Carregados \".concat(schedulings.length, \" agendamentos\"));\n            const formattedAppointments = schedulings.map((scheduling)=>{\n                var _APPOINTMENT_STATUS_, _APPOINTMENT_STATUS_1, _APPOINTMENT_STATUS_2, _APPOINTMENT_STATUS_3, _scheduling_Person_, _scheduling_Person, _scheduling_provider, _scheduling_Person_1, _scheduling_Person1, _scheduling_serviceType, _scheduling_insurance, _scheduling_Person2;\n                // Usar as datas exatamente como estão no banco de dados\n                // Não aplicar nenhuma conversão de fuso horário\n                console.log(\"[APPOINTMENT] \".concat(scheduling.title, \" - Original: \").concat(scheduling.startDate));\n                return {\n                    id: scheduling.id,\n                    title: scheduling.title || \"\",\n                    start: scheduling.startDate,\n                    end: scheduling.endDate,\n                    backgroundColor: isDarkMode ? (_APPOINTMENT_STATUS_ = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_ === void 0 ? void 0 : _APPOINTMENT_STATUS_.darkColor : (_APPOINTMENT_STATUS_1 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_1 === void 0 ? void 0 : _APPOINTMENT_STATUS_1.color,\n                    borderColor: isDarkMode ? (_APPOINTMENT_STATUS_2 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_2 === void 0 ? void 0 : _APPOINTMENT_STATUS_2.darkColor : (_APPOINTMENT_STATUS_3 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_3 === void 0 ? void 0 : _APPOINTMENT_STATUS_3.color,\n                    extendedProps: {\n                        description: scheduling.description || \"\",\n                        personfullName: ((_scheduling_Person = scheduling.Person) === null || _scheduling_Person === void 0 ? void 0 : (_scheduling_Person_ = _scheduling_Person[0]) === null || _scheduling_Person_ === void 0 ? void 0 : _scheduling_Person_.fullName) || scheduling.personfullName || \"\",\n                        providerfullName: ((_scheduling_provider = scheduling.provider) === null || _scheduling_provider === void 0 ? void 0 : _scheduling_provider.fullName) || scheduling.providerfullName || \"\",\n                        providerId: scheduling.userId || scheduling.providerId || \"\",\n                        // Garantir que o personId seja definido corretamente\n                        personId: ((_scheduling_Person1 = scheduling.Person) === null || _scheduling_Person1 === void 0 ? void 0 : (_scheduling_Person_1 = _scheduling_Person1[0]) === null || _scheduling_Person_1 === void 0 ? void 0 : _scheduling_Person_1.id) || scheduling.personId || scheduling.clientId || \"\",\n                        locationId: scheduling.locationId || \"\",\n                        serviceTypefullName: ((_scheduling_serviceType = scheduling.serviceType) === null || _scheduling_serviceType === void 0 ? void 0 : _scheduling_serviceType.name) || scheduling.serviceTypefullName || \"\",\n                        serviceTypeId: scheduling.serviceTypeId || \"\",\n                        status: scheduling.status || \"PENDING\",\n                        // Adicionar informações de convênio, se disponíveis\n                        insurance: scheduling.insurance || {},\n                        insuranceId: scheduling.insuranceId || ((_scheduling_insurance = scheduling.insurance) === null || _scheduling_insurance === void 0 ? void 0 : _scheduling_insurance.id) || \"\",\n                        insuranceInfo: scheduling.insuranceInfo || {},\n                        // Adicionar objetos completos para uso no modal\n                        person: ((_scheduling_Person2 = scheduling.Person) === null || _scheduling_Person2 === void 0 ? void 0 : _scheduling_Person2[0]) || scheduling.person || null,\n                        serviceType: scheduling.serviceType || null\n                    }\n                };\n            });\n            setAppointments(formattedAppointments);\n        } catch (error) {\n            console.error(\"Erro ao carregar agendamentos:\", error);\n            setAppointments([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Função para lidar com slots do calendário\n    const handleSlotClassNames = (slotInfo)=>{\n        // Só aplicamos essa lógica nas visualizações de semana e dia\n        if (currentView !== \"timeGridWeek\" && currentView !== \"timeGridDay\") {\n            return [\n                \"min-h-[120px] p-1\"\n            ];\n        }\n        return [\n            \"min-h-[120px] p-1\"\n        ];\n    };\n    // Função para lidar com a seleção de datas\n    const handleDateSelect = async (selectInfo, checkProvidersAvailability, setErrorMessage)=>{\n        var _selectInfo_end;\n        // Verificar permissão para criar agendamentos\n        if (!canCreateAppointment) {\n            setErrorMessage(\"Você não tem permissão para criar novos agendamentos.\");\n            return;\n        }\n        console.log(\"[DEBUG-SELECT] selectInfo recebido (horário local):\", {\n            start: selectInfo.start.toLocaleString(),\n            end: (_selectInfo_end = selectInfo.end) === null || _selectInfo_end === void 0 ? void 0 : _selectInfo_end.toLocaleString(),\n            dia: selectInfo.start.getDay(),\n            hora: selectInfo.start.getHours(),\n            view: currentView\n        });\n        // Usar os objetos Date diretamente, sem conversão para strings ISO\n        // Isso mantém o horário local que o usuário selecionou\n        console.log(\"[DEBUG-DURATION] Antes de calcular end time - selectInfo.end existe?\", !!selectInfo.end);\n        console.log(\"[DEBUG-DURATION] FORÇANDO duração padrão de 60 minutos, ignorando o valor de end\");\n        // Sempre usar 1 hora, independentemente do valor de end que o FullCalendar fornece\n        const endTime = new Date(selectInfo.start.getTime() + 60 * 60 * 1000);\n        console.log(\"[DEBUG-DURATION] End time forçado para 1 hora:\", endTime.toLocaleString());\n        console.log(\"[DEBUG-DURATION] Duração forçada (minutos):\", (endTime - selectInfo.start) / (60 * 1000));\n        const selectInfoLocal = {\n            ...selectInfo,\n            // Garantir que temos objetos Date válidos\n            start: selectInfo.start,\n            end: endTime\n        };\n        console.log(\"[DEBUG-DURATION] selectInfoLocal final:\", {\n            start: selectInfoLocal.start.toLocaleString(),\n            end: selectInfoLocal.end.toLocaleString(),\n            duracaoMinutos: (selectInfoLocal.end - selectInfoLocal.start) / (60 * 1000)\n        });\n        // Limpa mensagens de erro anteriores\n        setErrorMessage(null);\n        // Verificação específica para visualizações semanal e diária\n        if ((currentView === \"timeGridWeek\" || currentView === \"timeGridDay\") && filters.providers && filters.providers.length > 0) {\n            console.log(\"[SELECT] Realizando verificação de disponibilidade\");\n            // Verifica se algum provider está disponível no horário\n            const isAnyProviderAvailable = await checkProvidersAvailability(selectInfoLocal);\n            console.log(\"[SELECT] Resultado da verificação:\", isAnyProviderAvailable ? \"Disponível\" : \"Indisponível\");\n            if (!isAnyProviderAvailable) {\n                const errorMsg = \"Nenhum dos profissionais selecionados est\\xe1 dispon\\xedvel no hor\\xe1rio escolhido.\\n        Por favor, verifique o quadro de hor\\xe1rios ou selecione outro hor\\xe1rio.\";\n                console.log(\"[SELECT] Exibindo mensagem de erro:\", errorMsg);\n                setErrorMessage(errorMsg);\n                return;\n            }\n        }\n        console.log(\"[SELECT] Abrindo modal para criar novo agendamento\");\n        setSelectedDate(selectInfoLocal);\n        setSelectedAppointment(null);\n        setIsModalOpen(true);\n    };\n    // Função para lidar com cliques em eventos\n    const handleEventClick = async (clickInfo, checkSingleProviderAvailability, setErrorMessage)=>{\n        var _clickInfo_event_start, _clickInfo_event_end, _clickInfo_event_extendedProps, _filters_providers, _originalAppointment_Person_, _originalAppointment_Person, _originalAppointment_person, _originalAppointment_Person1, _originalAppointment_Person_1, _originalAppointment_Person2, _originalAppointment_person1;\n        // Verificar permissão para editar agendamentos\n        if (!canEditAppointment) {\n            // Para clientes, apenas mostrar os detalhes sem permitir edição\n            const event = clickInfo.event;\n            const extendedProps = event.extendedProps || {};\n            // Selecionar o agendamento para visualização (sem edição)\n            setSelectedAppointment({\n                id: event.id,\n                title: event.title,\n                startDate: event.start,\n                endDate: event.end,\n                ...extendedProps\n            });\n            // Abrir o modal (a verificação de permissão para edição será feita no modal)\n            setIsModalOpen(true);\n            return;\n        }\n        console.log(\"[EVENT-CLICK] handleEventClick chamado:\", {\n            view: currentView,\n            eventId: clickInfo.event.id,\n            eventTitle: clickInfo.event.title,\n            eventStart: (_clickInfo_event_start = clickInfo.event.start) === null || _clickInfo_event_start === void 0 ? void 0 : _clickInfo_event_start.toLocaleString(),\n            eventEnd: (_clickInfo_event_end = clickInfo.event.end) === null || _clickInfo_event_end === void 0 ? void 0 : _clickInfo_event_end.toLocaleString(),\n            providerId: (_clickInfo_event_extendedProps = clickInfo.event.extendedProps) === null || _clickInfo_event_extendedProps === void 0 ? void 0 : _clickInfo_event_extendedProps.providerId,\n            hasProviders: ((_filters_providers = filters.providers) === null || _filters_providers === void 0 ? void 0 : _filters_providers.length) > 0\n        });\n        const event = clickInfo.event;\n        const extendedProps = event.extendedProps || {};\n        const providerId = extendedProps.providerId;\n        // Verificação de disponibilidade na visualização semanal/diária\n        if ((currentView === \"timeGridWeek\" || currentView === \"timeGridDay\") && filters.providers && filters.providers.length > 0 && providerId) {\n            console.log(\"[EVENT-CLICK] Realizando verificação para evento clicado\");\n            // Verifica se o providerId do evento está entre os filtrados\n            const isProviderInFilters = filters.providers.includes(providerId);\n            console.log(\"[EVENT-CLICK] Provider est\\xe1 nos filtros? \".concat(isProviderInFilters));\n            if (isProviderInFilters) {\n                // Cria um objeto com formato similar ao selectInfo\n                // Usar os objetos Date diretamente, sem conversão para strings ISO\n                // SEMPRE usar duração de 1 hora, ignorando o valor de end\n                console.log(\"[EVENT-CLICK-DEBUG] FORÇANDO duração de 60 minutos para o evento\");\n                const dateInfo = {\n                    start: event.start,\n                    end: new Date(event.start.getTime() + 60 * 60 * 1000)\n                };\n                console.log(\"[EVENT-CLICK-DEBUG] Duração forçada (minutos):\", (dateInfo.end - dateInfo.start) / (60 * 1000));\n                // Verifica a disponibilidade\n                console.log(\"[EVENT-CLICK] Verificando disponibilidade para o evento\");\n                const isAvailable = await checkSingleProviderAvailability(providerId, dateInfo);\n                console.log(\"[EVENT-CLICK] Evento est\\xe1 dentro do hor\\xe1rio de trabalho? \".concat(isAvailable));\n                if (!isAvailable) {\n                    const errorMsg = \"Este hor\\xe1rio est\\xe1 fora do per\\xedodo de trabalho do profissional selecionado.\\n          Por favor, verifique o quadro de hor\\xe1rios ou selecione outro hor\\xe1rio.\";\n                    console.log(\"[EVENT-CLICK] Exibindo mensagem de erro:\", errorMsg);\n                    setErrorMessage(errorMsg);\n                    return;\n                }\n            }\n        }\n        console.log(\"[EVENT-CLICK] Abrindo modal para editar agendamento existente\");\n        // Encontrar o agendamento original nos dados carregados\n        // Primeiro, procurar pelo ID exato\n        let originalAppointment = appointments.find((a)=>a.id === event.id);\n        // Se não encontrar, tentar buscar pelo ID no extendedProps\n        if (!originalAppointment && extendedProps.id) {\n            originalAppointment = appointments.find((a)=>a.id === extendedProps.id);\n        }\n        // Se ainda não encontrar, usar os dados do evento diretamente\n        if (!originalAppointment) {\n            var _extendedProps_insurance;\n            console.error(\"[EVENT-CLICK] Agendamento não encontrado nos dados carregados. ID:\", event.id);\n            console.log(\"[EVENT-CLICK] Usando dados do evento diretamente\");\n            // Extrair dados diretamente do evento do calendário\n            const fallbackAppointment = {\n                id: event.id,\n                title: event.title,\n                description: extendedProps.description || \"\",\n                providerId: extendedProps.providerId || \"\",\n                personId: extendedProps.personId || \"\",\n                locationId: extendedProps.locationId || \"\",\n                serviceTypeId: extendedProps.serviceTypeId || \"\",\n                insuranceId: extendedProps.insuranceId || ((_extendedProps_insurance = extendedProps.insurance) === null || _extendedProps_insurance === void 0 ? void 0 : _extendedProps_insurance.id) || \"\",\n                startDate: event.start ? event.start.toISOString() : new Date().toISOString(),\n                endDate: event.end ? event.end.toISOString() : new Date().toISOString(),\n                status: extendedProps.status || \"PENDING\",\n                // Adicionar objetos completos para uso no modal\n                insurance: extendedProps.insurance || null,\n                serviceType: extendedProps.serviceType || null,\n                person: extendedProps.person || null,\n                location: extendedProps.location || null,\n                // Adicionar extendedProps completo para debug\n                extendedProps: extendedProps\n            };\n            console.log(\"[EVENT-CLICK] Dados do evento usados como fallback:\", fallbackAppointment);\n            setSelectedAppointment(fallbackAppointment);\n            setIsModalOpen(true);\n            return;\n        }\n        console.log(\"[EVENT-CLICK] Agendamento original encontrado:\", originalAppointment);\n        // Extrair o personId do array Person se existir\n        const personId = ((_originalAppointment_Person = originalAppointment.Person) === null || _originalAppointment_Person === void 0 ? void 0 : (_originalAppointment_Person_ = _originalAppointment_Person[0]) === null || _originalAppointment_Person_ === void 0 ? void 0 : _originalAppointment_Person_.id) || ((_originalAppointment_person = originalAppointment.person) === null || _originalAppointment_person === void 0 ? void 0 : _originalAppointment_person.id) || originalAppointment.personId || originalAppointment.clientId || extendedProps.personId || \"\";\n        // Criar o objeto de agendamento combinando dados do original e do evento\n        const appointment = {\n            id: originalAppointment.id || event.id,\n            title: originalAppointment.title || event.title || \"\",\n            description: originalAppointment.description || extendedProps.description || \"\",\n            providerId: originalAppointment.userId || originalAppointment.providerId || extendedProps.providerId || \"\",\n            personId: personId,\n            locationId: originalAppointment.locationId || extendedProps.locationId || \"\",\n            serviceTypeId: originalAppointment.serviceTypeId || extendedProps.serviceTypeId || \"\",\n            insuranceId: originalAppointment.insuranceId || extendedProps.insuranceId || \"\",\n            startDate: originalAppointment.startDate || (event.start ? event.start.toISOString() : new Date().toISOString()),\n            endDate: originalAppointment.endDate || (event.end ? event.end.toISOString() : new Date().toISOString()),\n            status: originalAppointment.status || extendedProps.status || \"PENDING\",\n            // Adicionar objetos completos para uso no modal\n            insurance: originalAppointment.insurance || extendedProps.insurance || null,\n            serviceType: originalAppointment.serviceType || extendedProps.serviceType || null,\n            person: ((_originalAppointment_Person1 = originalAppointment.Person) === null || _originalAppointment_Person1 === void 0 ? void 0 : _originalAppointment_Person1[0]) || originalAppointment.person || extendedProps.person || null,\n            location: originalAppointment.location || extendedProps.location || null,\n            // Adicionar extendedProps completo para debug\n            extendedProps: extendedProps\n        };\n        // Log detalhado para depuração\n        console.log(\"[EVENT-CLICK] Dados do personId:\", {\n            fromPerson: (_originalAppointment_Person2 = originalAppointment.Person) === null || _originalAppointment_Person2 === void 0 ? void 0 : (_originalAppointment_Person_1 = _originalAppointment_Person2[0]) === null || _originalAppointment_Person_1 === void 0 ? void 0 : _originalAppointment_Person_1.id,\n            fromPersonObj: (_originalAppointment_person1 = originalAppointment.person) === null || _originalAppointment_person1 === void 0 ? void 0 : _originalAppointment_person1.id,\n            fromPersonId: originalAppointment.personId,\n            fromClientId: originalAppointment.clientId,\n            fromExtendedProps: extendedProps.personId,\n            final: appointment.personId\n        });\n        console.log(\"[EVENT-CLICK] Agendamento criado a partir dos dados existentes:\", {\n            id: appointment.id,\n            title: appointment.title,\n            personId: appointment.personId,\n            insuranceId: appointment.insuranceId,\n            serviceTypeId: appointment.serviceTypeId,\n            startDate: appointment.startDate,\n            endDate: appointment.endDate\n        });\n        // Abrir o modal de agendamento com os dados do evento\n        setSelectedAppointment(appointment);\n        setIsModalOpen(true);\n    };\n    // Registra alterações na visualização do calendário\n    const handleDatesSet = (dateInfo)=>{\n        console.log(\"[CALENDAR] datesSet:\", dateInfo.view.type);\n        setCurrentView(dateInfo.view.type);\n    };\n    // Pesquisa de agendamentos\n    const handleSearch = async (searchFilters)=>{\n        console.log(\"[SEARCH] handleSearch chamado com filtros:\", searchFilters);\n        await loadAppointments(searchFilters);\n    };\n    return {\n        calendarRef,\n        isModalOpen,\n        setIsModalOpen,\n        selectedDate,\n        setSelectedDate,\n        selectedAppointment,\n        setSelectedAppointment,\n        appointments,\n        isLoading,\n        currentView,\n        handleSlotClassNames,\n        handleDateSelect,\n        handleEventClick,\n        handleDatesSet,\n        handleSearch,\n        loadAppointments\n    };\n};\n_s(useAppointmentCalendar, \"2cL1DwK4B71ANIrItpMj+ACfQio=\");\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAppointmentCalendar);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/scheduler/calendar/hooks/useAppointmentCalendar.js\n"));

/***/ })

});