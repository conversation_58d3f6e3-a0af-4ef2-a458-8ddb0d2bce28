"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/modules/scheduler/calendar/hooks/useAppointmentCalendar.js":
/*!****************************************************************************!*\
  !*** ./src/app/modules/scheduler/calendar/hooks/useAppointmentCalendar.js ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/modules/scheduler/services/appointmentService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/appointmentService.js\");\n/* harmony import */ var _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/appointmentConstants */ \"(app-pages-browser)/./src/app/modules/scheduler/calendar/utils/appointmentConstants.js\");\n/* harmony import */ var _utils_dateFormatters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/dateFormatters */ \"(app-pages-browser)/./src/utils/dateFormatters.js\");\n/* harmony import */ var _hooks_useSocket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSocket */ \"(app-pages-browser)/./src/hooks/useSocket.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\n\n\nconst useAppointmentCalendar = (filters, isDarkMode, permissions)=>{\n    _s();\n    const calendarRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [selectedAppointment, setSelectedAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"dayGridMonth\");\n    // Extrair permissões\n    const { canCreateAppointment, canEditAppointment, canDeleteAppointment } = permissions;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAppointmentCalendar.useEffect\": ()=>{\n            loadAppointments();\n        }\n    }[\"useAppointmentCalendar.useEffect\"], []);\n    // Recarregar os agendamentos quando o modo dark muda para ajustar as cores\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAppointmentCalendar.useEffect\": ()=>{\n            if (appointments.length > 0) {\n                const updatedAppointments = appointments.map({\n                    \"useAppointmentCalendar.useEffect.updatedAppointments\": (appointment)=>{\n                        var _APPOINTMENT_STATUS_, _APPOINTMENT_STATUS_1, _APPOINTMENT_STATUS_2, _APPOINTMENT_STATUS_3;\n                        return {\n                            ...appointment,\n                            backgroundColor: isDarkMode ? (_APPOINTMENT_STATUS_ = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_ === void 0 ? void 0 : _APPOINTMENT_STATUS_.darkColor : (_APPOINTMENT_STATUS_1 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_1 === void 0 ? void 0 : _APPOINTMENT_STATUS_1.color,\n                            borderColor: isDarkMode ? (_APPOINTMENT_STATUS_2 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_2 === void 0 ? void 0 : _APPOINTMENT_STATUS_2.darkColor : (_APPOINTMENT_STATUS_3 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_3 === void 0 ? void 0 : _APPOINTMENT_STATUS_3.color\n                        };\n                    }\n                }[\"useAppointmentCalendar.useEffect.updatedAppointments\"]);\n                setAppointments(updatedAppointments);\n            }\n        }\n    }[\"useAppointmentCalendar.useEffect\"], [\n        isDarkMode\n    ]);\n    const loadAppointments = async function() {\n        let searchFilters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : filters;\n        setIsLoading(true);\n        try {\n            // Para clientes, não enviamos o filtro de providers (profissionais)\n            const clientSafeFilters = {\n                ...searchFilters\n            };\n            // Se o usuário for cliente, remover o filtro de providers\n            if (clientSafeFilters.providers && clientSafeFilters.providers.length > 0) {\n                console.log(\"[CLIENT-FILTER] Removendo filtro de providers para cliente\");\n                delete clientSafeFilters.providers;\n            }\n            const response = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_1__.appointmentService.getAppointments(clientSafeFilters);\n            if (!(response === null || response === void 0 ? void 0 : response.schedulings) && !(response === null || response === void 0 ? void 0 : response.appointments)) {\n                console.error(\"Resposta inválida da API:\", response);\n                setAppointments([]);\n                return;\n            }\n            const schedulings = response.appointments || [];\n            console.log(\"[APPOINTMENTS] Carregados \".concat(schedulings.length, \" agendamentos\"));\n            const formattedAppointments = schedulings.map((scheduling)=>{\n                var _APPOINTMENT_STATUS_, _APPOINTMENT_STATUS_1, _APPOINTMENT_STATUS_2, _APPOINTMENT_STATUS_3, _scheduling_Person_, _scheduling_Person, _scheduling_provider, _scheduling_Person_1, _scheduling_Person1, _scheduling_serviceType, _scheduling_insurance, _scheduling_Person2;\n                // Usar as datas exatamente como estão no banco de dados\n                // Não aplicar nenhuma conversão de fuso horário\n                console.log(\"[APPOINTMENT] \".concat(scheduling.title, \" - Original: \").concat(scheduling.startDate));\n                return {\n                    id: scheduling.id,\n                    title: scheduling.title || \"\",\n                    start: scheduling.startDate,\n                    end: scheduling.endDate,\n                    backgroundColor: isDarkMode ? (_APPOINTMENT_STATUS_ = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_ === void 0 ? void 0 : _APPOINTMENT_STATUS_.darkColor : (_APPOINTMENT_STATUS_1 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_1 === void 0 ? void 0 : _APPOINTMENT_STATUS_1.color,\n                    borderColor: isDarkMode ? (_APPOINTMENT_STATUS_2 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_2 === void 0 ? void 0 : _APPOINTMENT_STATUS_2.darkColor : (_APPOINTMENT_STATUS_3 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_3 === void 0 ? void 0 : _APPOINTMENT_STATUS_3.color,\n                    extendedProps: {\n                        description: scheduling.description || \"\",\n                        personfullName: ((_scheduling_Person = scheduling.Person) === null || _scheduling_Person === void 0 ? void 0 : (_scheduling_Person_ = _scheduling_Person[0]) === null || _scheduling_Person_ === void 0 ? void 0 : _scheduling_Person_.fullName) || scheduling.personfullName || \"\",\n                        providerfullName: ((_scheduling_provider = scheduling.provider) === null || _scheduling_provider === void 0 ? void 0 : _scheduling_provider.fullName) || scheduling.providerfullName || \"\",\n                        providerId: scheduling.userId || scheduling.providerId || \"\",\n                        // Garantir que o personId seja definido corretamente\n                        personId: ((_scheduling_Person1 = scheduling.Person) === null || _scheduling_Person1 === void 0 ? void 0 : (_scheduling_Person_1 = _scheduling_Person1[0]) === null || _scheduling_Person_1 === void 0 ? void 0 : _scheduling_Person_1.id) || scheduling.personId || scheduling.clientId || \"\",\n                        locationId: scheduling.locationId || \"\",\n                        serviceTypefullName: ((_scheduling_serviceType = scheduling.serviceType) === null || _scheduling_serviceType === void 0 ? void 0 : _scheduling_serviceType.name) || scheduling.serviceTypefullName || \"\",\n                        serviceTypeId: scheduling.serviceTypeId || \"\",\n                        status: scheduling.status || \"PENDING\",\n                        // Adicionar informações de convênio, se disponíveis\n                        insurance: scheduling.insurance || {},\n                        insuranceId: scheduling.insuranceId || ((_scheduling_insurance = scheduling.insurance) === null || _scheduling_insurance === void 0 ? void 0 : _scheduling_insurance.id) || \"\",\n                        insuranceInfo: scheduling.insuranceInfo || {},\n                        // Adicionar objetos completos para uso no modal\n                        person: ((_scheduling_Person2 = scheduling.Person) === null || _scheduling_Person2 === void 0 ? void 0 : _scheduling_Person2[0]) || scheduling.person || null,\n                        serviceType: scheduling.serviceType || null\n                    }\n                };\n            });\n            setAppointments(formattedAppointments);\n        } catch (error) {\n            console.error(\"Erro ao carregar agendamentos:\", error);\n            setAppointments([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Função para lidar com slots do calendário\n    const handleSlotClassNames = (slotInfo)=>{\n        // Só aplicamos essa lógica nas visualizações de semana e dia\n        if (currentView !== \"timeGridWeek\" && currentView !== \"timeGridDay\") {\n            return [\n                \"min-h-[120px] p-1\"\n            ];\n        }\n        return [\n            \"min-h-[120px] p-1\"\n        ];\n    };\n    // Função para lidar com a seleção de datas\n    const handleDateSelect = async (selectInfo, checkProvidersAvailability, setErrorMessage)=>{\n        var _selectInfo_end;\n        // Verificar permissão para criar agendamentos\n        if (!canCreateAppointment) {\n            setErrorMessage(\"Você não tem permissão para criar novos agendamentos.\");\n            return;\n        }\n        console.log(\"[DEBUG-SELECT] selectInfo recebido (horário local):\", {\n            start: selectInfo.start.toLocaleString(),\n            end: (_selectInfo_end = selectInfo.end) === null || _selectInfo_end === void 0 ? void 0 : _selectInfo_end.toLocaleString(),\n            dia: selectInfo.start.getDay(),\n            hora: selectInfo.start.getHours(),\n            view: currentView\n        });\n        // Usar os objetos Date diretamente, sem conversão para strings ISO\n        // Isso mantém o horário local que o usuário selecionou\n        console.log(\"[DEBUG-DURATION] Antes de calcular end time - selectInfo.end existe?\", !!selectInfo.end);\n        console.log(\"[DEBUG-DURATION] FORÇANDO duração padrão de 60 minutos, ignorando o valor de end\");\n        // Sempre usar 1 hora, independentemente do valor de end que o FullCalendar fornece\n        const endTime = new Date(selectInfo.start.getTime() + 60 * 60 * 1000);\n        console.log(\"[DEBUG-DURATION] End time forçado para 1 hora:\", endTime.toLocaleString());\n        console.log(\"[DEBUG-DURATION] Duração forçada (minutos):\", (endTime - selectInfo.start) / (60 * 1000));\n        const selectInfoLocal = {\n            ...selectInfo,\n            // Garantir que temos objetos Date válidos\n            start: selectInfo.start,\n            end: endTime\n        };\n        console.log(\"[DEBUG-DURATION] selectInfoLocal final:\", {\n            start: selectInfoLocal.start.toLocaleString(),\n            end: selectInfoLocal.end.toLocaleString(),\n            duracaoMinutos: (selectInfoLocal.end - selectInfoLocal.start) / (60 * 1000)\n        });\n        // Limpa mensagens de erro anteriores\n        setErrorMessage(null);\n        // Verificação específica para visualizações semanal e diária\n        if ((currentView === \"timeGridWeek\" || currentView === \"timeGridDay\") && filters.providers && filters.providers.length > 0) {\n            console.log(\"[SELECT] Realizando verificação de disponibilidade\");\n            // Verifica se algum provider está disponível no horário\n            const isAnyProviderAvailable = await checkProvidersAvailability(selectInfoLocal);\n            console.log(\"[SELECT] Resultado da verificação:\", isAnyProviderAvailable ? \"Disponível\" : \"Indisponível\");\n            if (!isAnyProviderAvailable) {\n                const errorMsg = \"Nenhum dos profissionais selecionados est\\xe1 dispon\\xedvel no hor\\xe1rio escolhido.\\n        Por favor, verifique o quadro de hor\\xe1rios ou selecione outro hor\\xe1rio.\";\n                console.log(\"[SELECT] Exibindo mensagem de erro:\", errorMsg);\n                setErrorMessage(errorMsg);\n                return;\n            }\n        }\n        console.log(\"[SELECT] Abrindo modal para criar novo agendamento\");\n        setSelectedDate(selectInfoLocal);\n        setSelectedAppointment(null);\n        setIsModalOpen(true);\n    };\n    // Função para lidar com cliques em eventos\n    const handleEventClick = async (clickInfo, checkSingleProviderAvailability, setErrorMessage)=>{\n        var _clickInfo_event_start, _clickInfo_event_end, _clickInfo_event_extendedProps, _filters_providers, _originalAppointment_Person_, _originalAppointment_Person, _originalAppointment_person, _originalAppointment_Person1, _originalAppointment_Person_1, _originalAppointment_Person2, _originalAppointment_person1;\n        // Verificar permissão para editar agendamentos\n        if (!canEditAppointment) {\n            // Para clientes, apenas mostrar os detalhes sem permitir edição\n            const event = clickInfo.event;\n            const extendedProps = event.extendedProps || {};\n            // Selecionar o agendamento para visualização (sem edição)\n            setSelectedAppointment({\n                id: event.id,\n                title: event.title,\n                startDate: event.start,\n                endDate: event.end,\n                ...extendedProps\n            });\n            // Abrir o modal (a verificação de permissão para edição será feita no modal)\n            setIsModalOpen(true);\n            return;\n        }\n        console.log(\"[EVENT-CLICK] handleEventClick chamado:\", {\n            view: currentView,\n            eventId: clickInfo.event.id,\n            eventTitle: clickInfo.event.title,\n            eventStart: (_clickInfo_event_start = clickInfo.event.start) === null || _clickInfo_event_start === void 0 ? void 0 : _clickInfo_event_start.toLocaleString(),\n            eventEnd: (_clickInfo_event_end = clickInfo.event.end) === null || _clickInfo_event_end === void 0 ? void 0 : _clickInfo_event_end.toLocaleString(),\n            providerId: (_clickInfo_event_extendedProps = clickInfo.event.extendedProps) === null || _clickInfo_event_extendedProps === void 0 ? void 0 : _clickInfo_event_extendedProps.providerId,\n            hasProviders: ((_filters_providers = filters.providers) === null || _filters_providers === void 0 ? void 0 : _filters_providers.length) > 0\n        });\n        const event = clickInfo.event;\n        const extendedProps = event.extendedProps || {};\n        const providerId = extendedProps.providerId;\n        // Verificação de disponibilidade na visualização semanal/diária\n        if ((currentView === \"timeGridWeek\" || currentView === \"timeGridDay\") && filters.providers && filters.providers.length > 0 && providerId) {\n            console.log(\"[EVENT-CLICK] Realizando verificação para evento clicado\");\n            // Verifica se o providerId do evento está entre os filtrados\n            const isProviderInFilters = filters.providers.includes(providerId);\n            console.log(\"[EVENT-CLICK] Provider est\\xe1 nos filtros? \".concat(isProviderInFilters));\n            if (isProviderInFilters) {\n                // Cria um objeto com formato similar ao selectInfo\n                // Usar os objetos Date diretamente, sem conversão para strings ISO\n                // SEMPRE usar duração de 1 hora, ignorando o valor de end\n                console.log(\"[EVENT-CLICK-DEBUG] FORÇANDO duração de 60 minutos para o evento\");\n                const dateInfo = {\n                    start: event.start,\n                    end: new Date(event.start.getTime() + 60 * 60 * 1000)\n                };\n                console.log(\"[EVENT-CLICK-DEBUG] Duração forçada (minutos):\", (dateInfo.end - dateInfo.start) / (60 * 1000));\n                // Verifica a disponibilidade\n                console.log(\"[EVENT-CLICK] Verificando disponibilidade para o evento\");\n                const isAvailable = await checkSingleProviderAvailability(providerId, dateInfo);\n                console.log(\"[EVENT-CLICK] Evento est\\xe1 dentro do hor\\xe1rio de trabalho? \".concat(isAvailable));\n                if (!isAvailable) {\n                    const errorMsg = \"Este hor\\xe1rio est\\xe1 fora do per\\xedodo de trabalho do profissional selecionado.\\n          Por favor, verifique o quadro de hor\\xe1rios ou selecione outro hor\\xe1rio.\";\n                    console.log(\"[EVENT-CLICK] Exibindo mensagem de erro:\", errorMsg);\n                    setErrorMessage(errorMsg);\n                    return;\n                }\n            }\n        }\n        console.log(\"[EVENT-CLICK] Abrindo modal para editar agendamento existente\");\n        // Encontrar o agendamento original nos dados carregados\n        // Primeiro, procurar pelo ID exato\n        let originalAppointment = appointments.find((a)=>a.id === event.id);\n        // Se não encontrar, tentar buscar pelo ID no extendedProps\n        if (!originalAppointment && extendedProps.id) {\n            originalAppointment = appointments.find((a)=>a.id === extendedProps.id);\n        }\n        // Se ainda não encontrar, usar os dados do evento diretamente\n        if (!originalAppointment) {\n            var _extendedProps_insurance;\n            console.error(\"[EVENT-CLICK] Agendamento não encontrado nos dados carregados. ID:\", event.id);\n            console.log(\"[EVENT-CLICK] Usando dados do evento diretamente\");\n            // Extrair dados diretamente do evento do calendário\n            const fallbackAppointment = {\n                id: event.id,\n                title: event.title,\n                description: extendedProps.description || \"\",\n                providerId: extendedProps.providerId || \"\",\n                personId: extendedProps.personId || \"\",\n                locationId: extendedProps.locationId || \"\",\n                serviceTypeId: extendedProps.serviceTypeId || \"\",\n                insuranceId: extendedProps.insuranceId || ((_extendedProps_insurance = extendedProps.insurance) === null || _extendedProps_insurance === void 0 ? void 0 : _extendedProps_insurance.id) || \"\",\n                startDate: event.start ? event.start.toISOString() : new Date().toISOString(),\n                endDate: event.end ? event.end.toISOString() : new Date().toISOString(),\n                status: extendedProps.status || \"PENDING\",\n                // Adicionar objetos completos para uso no modal\n                insurance: extendedProps.insurance || null,\n                serviceType: extendedProps.serviceType || null,\n                person: extendedProps.person || null,\n                location: extendedProps.location || null,\n                // Adicionar extendedProps completo para debug\n                extendedProps: extendedProps\n            };\n            console.log(\"[EVENT-CLICK] Dados do evento usados como fallback:\", fallbackAppointment);\n            setSelectedAppointment(fallbackAppointment);\n            setIsModalOpen(true);\n            return;\n        }\n        console.log(\"[EVENT-CLICK] Agendamento original encontrado:\", originalAppointment);\n        // Extrair o personId do array Person se existir\n        const personId = ((_originalAppointment_Person = originalAppointment.Person) === null || _originalAppointment_Person === void 0 ? void 0 : (_originalAppointment_Person_ = _originalAppointment_Person[0]) === null || _originalAppointment_Person_ === void 0 ? void 0 : _originalAppointment_Person_.id) || ((_originalAppointment_person = originalAppointment.person) === null || _originalAppointment_person === void 0 ? void 0 : _originalAppointment_person.id) || originalAppointment.personId || originalAppointment.clientId || extendedProps.personId || \"\";\n        // Criar o objeto de agendamento combinando dados do original e do evento\n        const appointment = {\n            id: originalAppointment.id || event.id,\n            title: originalAppointment.title || event.title || \"\",\n            description: originalAppointment.description || extendedProps.description || \"\",\n            providerId: originalAppointment.userId || originalAppointment.providerId || extendedProps.providerId || \"\",\n            personId: personId,\n            locationId: originalAppointment.locationId || extendedProps.locationId || \"\",\n            serviceTypeId: originalAppointment.serviceTypeId || extendedProps.serviceTypeId || \"\",\n            insuranceId: originalAppointment.insuranceId || extendedProps.insuranceId || \"\",\n            startDate: originalAppointment.startDate || (event.start ? event.start.toISOString() : new Date().toISOString()),\n            endDate: originalAppointment.endDate || (event.end ? event.end.toISOString() : new Date().toISOString()),\n            status: originalAppointment.status || extendedProps.status || \"PENDING\",\n            // Adicionar objetos completos para uso no modal\n            insurance: originalAppointment.insurance || extendedProps.insurance || null,\n            serviceType: originalAppointment.serviceType || extendedProps.serviceType || null,\n            person: ((_originalAppointment_Person1 = originalAppointment.Person) === null || _originalAppointment_Person1 === void 0 ? void 0 : _originalAppointment_Person1[0]) || originalAppointment.person || extendedProps.person || null,\n            location: originalAppointment.location || extendedProps.location || null,\n            // Adicionar extendedProps completo para debug\n            extendedProps: extendedProps\n        };\n        // Log detalhado para depuração\n        console.log(\"[EVENT-CLICK] Dados do personId:\", {\n            fromPerson: (_originalAppointment_Person2 = originalAppointment.Person) === null || _originalAppointment_Person2 === void 0 ? void 0 : (_originalAppointment_Person_1 = _originalAppointment_Person2[0]) === null || _originalAppointment_Person_1 === void 0 ? void 0 : _originalAppointment_Person_1.id,\n            fromPersonObj: (_originalAppointment_person1 = originalAppointment.person) === null || _originalAppointment_person1 === void 0 ? void 0 : _originalAppointment_person1.id,\n            fromPersonId: originalAppointment.personId,\n            fromClientId: originalAppointment.clientId,\n            fromExtendedProps: extendedProps.personId,\n            final: appointment.personId\n        });\n        console.log(\"[EVENT-CLICK] Agendamento criado a partir dos dados existentes:\", {\n            id: appointment.id,\n            title: appointment.title,\n            personId: appointment.personId,\n            insuranceId: appointment.insuranceId,\n            serviceTypeId: appointment.serviceTypeId,\n            startDate: appointment.startDate,\n            endDate: appointment.endDate\n        });\n        // Abrir o modal de agendamento com os dados do evento\n        setSelectedAppointment(appointment);\n        setIsModalOpen(true);\n    };\n    // Registra alterações na visualização do calendário\n    const handleDatesSet = (dateInfo)=>{\n        console.log(\"[CALENDAR] datesSet:\", dateInfo.view.type);\n        setCurrentView(dateInfo.view.type);\n    };\n    // Pesquisa de agendamentos\n    const handleSearch = async (searchFilters)=>{\n        console.log(\"[SEARCH] handleSearch chamado com filtros:\", searchFilters);\n        await loadAppointments(searchFilters);\n    };\n    return {\n        calendarRef,\n        isModalOpen,\n        setIsModalOpen,\n        selectedDate,\n        setSelectedDate,\n        selectedAppointment,\n        setSelectedAppointment,\n        appointments,\n        isLoading,\n        currentView,\n        handleSlotClassNames,\n        handleDateSelect,\n        handleEventClick,\n        handleDatesSet,\n        handleSearch,\n        loadAppointments\n    };\n};\n_s(useAppointmentCalendar, \"+Z0cxrJwpffyjMJyl3IS1JdYkcA=\");\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAppointmentCalendar);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/scheduler/calendar/hooks/useAppointmentCalendar.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useSocket.js":
/*!********************************!*\
  !*** ./src/hooks/useSocket.js ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\nvar _s = $RefreshSig$();\n\n\n// Função auxiliar para obter o token atual\nconst getCurrentToken = ()=>{\n    return localStorage.getItem('token');\n};\nfunction useSocket(url) {\n    _s();\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSocket.useEffect\": ()=>{\n            // Verificar se o usuário está autenticado\n            const token = getCurrentToken();\n            if (!token) return;\n            // Criar instância do socket\n            const socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(url, {\n                auth: {\n                    token\n                },\n                reconnectionAttempts: 5,\n                reconnectionDelay: 1000,\n                autoConnect: true\n            });\n            // Configurar handlers de conexão\n            socketInstance.on('connect', {\n                \"useSocket.useEffect\": ()=>{\n                    console.log('Socket conectado:', socketInstance.id);\n                }\n            }[\"useSocket.useEffect\"]);\n            socketInstance.on('connect_error', {\n                \"useSocket.useEffect\": (err)=>{\n                    console.error('Erro de conexão:', err.message);\n                }\n            }[\"useSocket.useEffect\"]);\n            socketInstance.on('disconnect', {\n                \"useSocket.useEffect\": (reason)=>{\n                    console.log('Socket desconectado:', reason);\n                }\n            }[\"useSocket.useEffect\"]);\n            setSocket(socketInstance);\n            // Limpar socket ao desmontar\n            return ({\n                \"useSocket.useEffect\": ()=>{\n                    if (socketInstance) {\n                        socketInstance.disconnect();\n                    }\n                }\n            })[\"useSocket.useEffect\"];\n        }\n    }[\"useSocket.useEffect\"], [\n        url\n    ]);\n    return socket;\n}\n_s(useSocket, \"NvwYO9vJOwIMt5STdlMKfWhuxZw=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSocket.js\n"));

/***/ })

});