"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/modules/scheduler/calendar/hooks/useAppointmentCalendar.js":
/*!****************************************************************************!*\
  !*** ./src/app/modules/scheduler/calendar/hooks/useAppointmentCalendar.js ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/modules/scheduler/services/appointmentService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/appointmentService.js\");\n/* harmony import */ var _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/appointmentConstants */ \"(app-pages-browser)/./src/app/modules/scheduler/calendar/utils/appointmentConstants.js\");\n/* harmony import */ var _utils_dateFormatters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/dateFormatters */ \"(app-pages-browser)/./src/utils/dateFormatters.js\");\n/* harmony import */ var _hooks_useSocket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSocket */ \"(app-pages-browser)/./src/hooks/useSocket.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\n\n\nconst useAppointmentCalendar = (filters, isDarkMode, permissions)=>{\n    _s();\n    const calendarRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [selectedAppointment, setSelectedAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"dayGridMonth\");\n    // Extrair permissões\n    const { canCreateAppointment, canEditAppointment, canDeleteAppointment } = permissions;\n    // Socket.IO para atualizações em tempo real\n    const socket = (0,_hooks_useSocket__WEBPACK_IMPORTED_MODULE_4__.useSocket)(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000');\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAppointmentCalendar.useEffect\": ()=>{\n            loadAppointments();\n        }\n    }[\"useAppointmentCalendar.useEffect\"], []);\n    // Socket.IO listeners para atualizações em tempo real\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAppointmentCalendar.useEffect\": ()=>{\n            if (!socket || typeof socket.on !== 'function') {\n                console.log('[CALENDAR-SOCKET] Socket não disponível ou inválido');\n                return;\n            }\n            console.log('[CALENDAR-SOCKET] Configurando listeners Socket.IO');\n            // Listener para agendamentos criados\n            const handleAppointmentCreated = {\n                \"useAppointmentCalendar.useEffect.handleAppointmentCreated\": (data)=>{\n                    console.log('[CALENDAR-SOCKET] Agendamento(s) criado(s):', data);\n                    // Recarregar agendamentos para garantir que temos os dados mais atuais\n                    loadAppointments();\n                }\n            }[\"useAppointmentCalendar.useEffect.handleAppointmentCreated\"];\n            // Listener para agendamentos atualizados\n            const handleAppointmentUpdated = {\n                \"useAppointmentCalendar.useEffect.handleAppointmentUpdated\": (data)=>{\n                    console.log('[CALENDAR-SOCKET] Agendamento atualizado:', data);\n                    // Recarregar agendamentos para garantir que temos os dados mais atuais\n                    loadAppointments();\n                }\n            }[\"useAppointmentCalendar.useEffect.handleAppointmentUpdated\"];\n            // Listener para agendamentos deletados\n            const handleAppointmentDeleted = {\n                \"useAppointmentCalendar.useEffect.handleAppointmentDeleted\": (data)=>{\n                    console.log('[CALENDAR-SOCKET] Agendamento deletado:', data);\n                    // Recarregar agendamentos para garantir que temos os dados mais atuais\n                    loadAppointments();\n                }\n            }[\"useAppointmentCalendar.useEffect.handleAppointmentDeleted\"];\n            // Registrar listeners\n            socket.on('appointment:created', handleAppointmentCreated);\n            socket.on('appointment:updated', handleAppointmentUpdated);\n            socket.on('appointment:deleted', handleAppointmentDeleted);\n            // Cleanup listeners\n            return ({\n                \"useAppointmentCalendar.useEffect\": ()=>{\n                    console.log('[CALENDAR-SOCKET] Removendo listeners Socket.IO');\n                    if (socket && typeof socket.off === 'function') {\n                        socket.off('appointment:created', handleAppointmentCreated);\n                        socket.off('appointment:updated', handleAppointmentUpdated);\n                        socket.off('appointment:deleted', handleAppointmentDeleted);\n                    }\n                }\n            })[\"useAppointmentCalendar.useEffect\"];\n        }\n    }[\"useAppointmentCalendar.useEffect\"], [\n        socket\n    ]);\n    // Recarregar os agendamentos quando o modo dark muda para ajustar as cores\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAppointmentCalendar.useEffect\": ()=>{\n            if (appointments.length > 0) {\n                const updatedAppointments = appointments.map({\n                    \"useAppointmentCalendar.useEffect.updatedAppointments\": (appointment)=>{\n                        var _APPOINTMENT_STATUS_, _APPOINTMENT_STATUS_1, _APPOINTMENT_STATUS_2, _APPOINTMENT_STATUS_3;\n                        return {\n                            ...appointment,\n                            backgroundColor: isDarkMode ? (_APPOINTMENT_STATUS_ = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_ === void 0 ? void 0 : _APPOINTMENT_STATUS_.darkColor : (_APPOINTMENT_STATUS_1 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_1 === void 0 ? void 0 : _APPOINTMENT_STATUS_1.color,\n                            borderColor: isDarkMode ? (_APPOINTMENT_STATUS_2 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_2 === void 0 ? void 0 : _APPOINTMENT_STATUS_2.darkColor : (_APPOINTMENT_STATUS_3 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_3 === void 0 ? void 0 : _APPOINTMENT_STATUS_3.color\n                        };\n                    }\n                }[\"useAppointmentCalendar.useEffect.updatedAppointments\"]);\n                setAppointments(updatedAppointments);\n            }\n        }\n    }[\"useAppointmentCalendar.useEffect\"], [\n        isDarkMode\n    ]);\n    const loadAppointments = async function() {\n        let searchFilters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : filters;\n        setIsLoading(true);\n        try {\n            // Para clientes, não enviamos o filtro de providers (profissionais)\n            const clientSafeFilters = {\n                ...searchFilters\n            };\n            // Se o usuário for cliente, remover o filtro de providers\n            if (clientSafeFilters.providers && clientSafeFilters.providers.length > 0) {\n                console.log(\"[CLIENT-FILTER] Removendo filtro de providers para cliente\");\n                delete clientSafeFilters.providers;\n            }\n            const response = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_1__.appointmentService.getAppointments(clientSafeFilters);\n            if (!(response === null || response === void 0 ? void 0 : response.schedulings) && !(response === null || response === void 0 ? void 0 : response.appointments)) {\n                console.error(\"Resposta inválida da API:\", response);\n                setAppointments([]);\n                return;\n            }\n            const schedulings = response.appointments || [];\n            console.log(\"[APPOINTMENTS] Carregados \".concat(schedulings.length, \" agendamentos\"));\n            const formattedAppointments = schedulings.map((scheduling)=>{\n                var _APPOINTMENT_STATUS_, _APPOINTMENT_STATUS_1, _APPOINTMENT_STATUS_2, _APPOINTMENT_STATUS_3, _scheduling_Person_, _scheduling_Person, _scheduling_provider, _scheduling_Person_1, _scheduling_Person1, _scheduling_serviceType, _scheduling_insurance, _scheduling_Person2;\n                // Usar as datas exatamente como estão no banco de dados\n                // Não aplicar nenhuma conversão de fuso horário\n                console.log(\"[APPOINTMENT] \".concat(scheduling.title, \" - Original: \").concat(scheduling.startDate));\n                return {\n                    id: scheduling.id,\n                    title: scheduling.title || \"\",\n                    start: scheduling.startDate,\n                    end: scheduling.endDate,\n                    backgroundColor: isDarkMode ? (_APPOINTMENT_STATUS_ = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_ === void 0 ? void 0 : _APPOINTMENT_STATUS_.darkColor : (_APPOINTMENT_STATUS_1 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_1 === void 0 ? void 0 : _APPOINTMENT_STATUS_1.color,\n                    borderColor: isDarkMode ? (_APPOINTMENT_STATUS_2 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_2 === void 0 ? void 0 : _APPOINTMENT_STATUS_2.darkColor : (_APPOINTMENT_STATUS_3 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_3 === void 0 ? void 0 : _APPOINTMENT_STATUS_3.color,\n                    extendedProps: {\n                        description: scheduling.description || \"\",\n                        personfullName: ((_scheduling_Person = scheduling.Person) === null || _scheduling_Person === void 0 ? void 0 : (_scheduling_Person_ = _scheduling_Person[0]) === null || _scheduling_Person_ === void 0 ? void 0 : _scheduling_Person_.fullName) || scheduling.personfullName || \"\",\n                        providerfullName: ((_scheduling_provider = scheduling.provider) === null || _scheduling_provider === void 0 ? void 0 : _scheduling_provider.fullName) || scheduling.providerfullName || \"\",\n                        providerId: scheduling.userId || scheduling.providerId || \"\",\n                        // Garantir que o personId seja definido corretamente\n                        personId: ((_scheduling_Person1 = scheduling.Person) === null || _scheduling_Person1 === void 0 ? void 0 : (_scheduling_Person_1 = _scheduling_Person1[0]) === null || _scheduling_Person_1 === void 0 ? void 0 : _scheduling_Person_1.id) || scheduling.personId || scheduling.clientId || \"\",\n                        locationId: scheduling.locationId || \"\",\n                        serviceTypefullName: ((_scheduling_serviceType = scheduling.serviceType) === null || _scheduling_serviceType === void 0 ? void 0 : _scheduling_serviceType.name) || scheduling.serviceTypefullName || \"\",\n                        serviceTypeId: scheduling.serviceTypeId || \"\",\n                        status: scheduling.status || \"PENDING\",\n                        // Adicionar informações de convênio, se disponíveis\n                        insurance: scheduling.insurance || {},\n                        insuranceId: scheduling.insuranceId || ((_scheduling_insurance = scheduling.insurance) === null || _scheduling_insurance === void 0 ? void 0 : _scheduling_insurance.id) || \"\",\n                        insuranceInfo: scheduling.insuranceInfo || {},\n                        // Adicionar objetos completos para uso no modal\n                        person: ((_scheduling_Person2 = scheduling.Person) === null || _scheduling_Person2 === void 0 ? void 0 : _scheduling_Person2[0]) || scheduling.person || null,\n                        serviceType: scheduling.serviceType || null\n                    }\n                };\n            });\n            setAppointments(formattedAppointments);\n        } catch (error) {\n            console.error(\"Erro ao carregar agendamentos:\", error);\n            setAppointments([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Função para lidar com slots do calendário\n    const handleSlotClassNames = (slotInfo)=>{\n        // Só aplicamos essa lógica nas visualizações de semana e dia\n        if (currentView !== \"timeGridWeek\" && currentView !== \"timeGridDay\") {\n            return [\n                \"min-h-[120px] p-1\"\n            ];\n        }\n        return [\n            \"min-h-[120px] p-1\"\n        ];\n    };\n    // Função para lidar com a seleção de datas\n    const handleDateSelect = async (selectInfo, checkProvidersAvailability, setErrorMessage)=>{\n        var _selectInfo_end;\n        // Verificar permissão para criar agendamentos\n        if (!canCreateAppointment) {\n            setErrorMessage(\"Você não tem permissão para criar novos agendamentos.\");\n            return;\n        }\n        console.log(\"[DEBUG-SELECT] selectInfo recebido (horário local):\", {\n            start: selectInfo.start.toLocaleString(),\n            end: (_selectInfo_end = selectInfo.end) === null || _selectInfo_end === void 0 ? void 0 : _selectInfo_end.toLocaleString(),\n            dia: selectInfo.start.getDay(),\n            hora: selectInfo.start.getHours(),\n            view: currentView\n        });\n        // Usar os objetos Date diretamente, sem conversão para strings ISO\n        // Isso mantém o horário local que o usuário selecionou\n        console.log(\"[DEBUG-DURATION] Antes de calcular end time - selectInfo.end existe?\", !!selectInfo.end);\n        console.log(\"[DEBUG-DURATION] FORÇANDO duração padrão de 60 minutos, ignorando o valor de end\");\n        // Sempre usar 1 hora, independentemente do valor de end que o FullCalendar fornece\n        const endTime = new Date(selectInfo.start.getTime() + 60 * 60 * 1000);\n        console.log(\"[DEBUG-DURATION] End time forçado para 1 hora:\", endTime.toLocaleString());\n        console.log(\"[DEBUG-DURATION] Duração forçada (minutos):\", (endTime - selectInfo.start) / (60 * 1000));\n        const selectInfoLocal = {\n            ...selectInfo,\n            // Garantir que temos objetos Date válidos\n            start: selectInfo.start,\n            end: endTime\n        };\n        console.log(\"[DEBUG-DURATION] selectInfoLocal final:\", {\n            start: selectInfoLocal.start.toLocaleString(),\n            end: selectInfoLocal.end.toLocaleString(),\n            duracaoMinutos: (selectInfoLocal.end - selectInfoLocal.start) / (60 * 1000)\n        });\n        // Limpa mensagens de erro anteriores\n        setErrorMessage(null);\n        // Verificação específica para visualizações semanal e diária\n        if ((currentView === \"timeGridWeek\" || currentView === \"timeGridDay\") && filters.providers && filters.providers.length > 0) {\n            console.log(\"[SELECT] Realizando verificação de disponibilidade\");\n            // Verifica se algum provider está disponível no horário\n            const isAnyProviderAvailable = await checkProvidersAvailability(selectInfoLocal);\n            console.log(\"[SELECT] Resultado da verificação:\", isAnyProviderAvailable ? \"Disponível\" : \"Indisponível\");\n            if (!isAnyProviderAvailable) {\n                const errorMsg = \"Nenhum dos profissionais selecionados est\\xe1 dispon\\xedvel no hor\\xe1rio escolhido.\\n        Por favor, verifique o quadro de hor\\xe1rios ou selecione outro hor\\xe1rio.\";\n                console.log(\"[SELECT] Exibindo mensagem de erro:\", errorMsg);\n                setErrorMessage(errorMsg);\n                return;\n            }\n        }\n        console.log(\"[SELECT] Abrindo modal para criar novo agendamento\");\n        setSelectedDate(selectInfoLocal);\n        setSelectedAppointment(null);\n        setIsModalOpen(true);\n    };\n    // Função para lidar com cliques em eventos\n    const handleEventClick = async (clickInfo, checkSingleProviderAvailability, setErrorMessage)=>{\n        var _clickInfo_event_start, _clickInfo_event_end, _clickInfo_event_extendedProps, _filters_providers, _originalAppointment_Person_, _originalAppointment_Person, _originalAppointment_person, _originalAppointment_Person1, _originalAppointment_Person_1, _originalAppointment_Person2, _originalAppointment_person1;\n        // Verificar permissão para editar agendamentos\n        if (!canEditAppointment) {\n            // Para clientes, apenas mostrar os detalhes sem permitir edição\n            const event = clickInfo.event;\n            const extendedProps = event.extendedProps || {};\n            // Selecionar o agendamento para visualização (sem edição)\n            setSelectedAppointment({\n                id: event.id,\n                title: event.title,\n                startDate: event.start,\n                endDate: event.end,\n                ...extendedProps\n            });\n            // Abrir o modal (a verificação de permissão para edição será feita no modal)\n            setIsModalOpen(true);\n            return;\n        }\n        console.log(\"[EVENT-CLICK] handleEventClick chamado:\", {\n            view: currentView,\n            eventId: clickInfo.event.id,\n            eventTitle: clickInfo.event.title,\n            eventStart: (_clickInfo_event_start = clickInfo.event.start) === null || _clickInfo_event_start === void 0 ? void 0 : _clickInfo_event_start.toLocaleString(),\n            eventEnd: (_clickInfo_event_end = clickInfo.event.end) === null || _clickInfo_event_end === void 0 ? void 0 : _clickInfo_event_end.toLocaleString(),\n            providerId: (_clickInfo_event_extendedProps = clickInfo.event.extendedProps) === null || _clickInfo_event_extendedProps === void 0 ? void 0 : _clickInfo_event_extendedProps.providerId,\n            hasProviders: ((_filters_providers = filters.providers) === null || _filters_providers === void 0 ? void 0 : _filters_providers.length) > 0\n        });\n        const event = clickInfo.event;\n        const extendedProps = event.extendedProps || {};\n        const providerId = extendedProps.providerId;\n        // Verificação de disponibilidade na visualização semanal/diária\n        if ((currentView === \"timeGridWeek\" || currentView === \"timeGridDay\") && filters.providers && filters.providers.length > 0 && providerId) {\n            console.log(\"[EVENT-CLICK] Realizando verificação para evento clicado\");\n            // Verifica se o providerId do evento está entre os filtrados\n            const isProviderInFilters = filters.providers.includes(providerId);\n            console.log(\"[EVENT-CLICK] Provider est\\xe1 nos filtros? \".concat(isProviderInFilters));\n            if (isProviderInFilters) {\n                // Cria um objeto com formato similar ao selectInfo\n                // Usar os objetos Date diretamente, sem conversão para strings ISO\n                // SEMPRE usar duração de 1 hora, ignorando o valor de end\n                console.log(\"[EVENT-CLICK-DEBUG] FORÇANDO duração de 60 minutos para o evento\");\n                const dateInfo = {\n                    start: event.start,\n                    end: new Date(event.start.getTime() + 60 * 60 * 1000)\n                };\n                console.log(\"[EVENT-CLICK-DEBUG] Duração forçada (minutos):\", (dateInfo.end - dateInfo.start) / (60 * 1000));\n                // Verifica a disponibilidade\n                console.log(\"[EVENT-CLICK] Verificando disponibilidade para o evento\");\n                const isAvailable = await checkSingleProviderAvailability(providerId, dateInfo);\n                console.log(\"[EVENT-CLICK] Evento est\\xe1 dentro do hor\\xe1rio de trabalho? \".concat(isAvailable));\n                if (!isAvailable) {\n                    const errorMsg = \"Este hor\\xe1rio est\\xe1 fora do per\\xedodo de trabalho do profissional selecionado.\\n          Por favor, verifique o quadro de hor\\xe1rios ou selecione outro hor\\xe1rio.\";\n                    console.log(\"[EVENT-CLICK] Exibindo mensagem de erro:\", errorMsg);\n                    setErrorMessage(errorMsg);\n                    return;\n                }\n            }\n        }\n        console.log(\"[EVENT-CLICK] Abrindo modal para editar agendamento existente\");\n        // Encontrar o agendamento original nos dados carregados\n        // Primeiro, procurar pelo ID exato\n        let originalAppointment = appointments.find((a)=>a.id === event.id);\n        // Se não encontrar, tentar buscar pelo ID no extendedProps\n        if (!originalAppointment && extendedProps.id) {\n            originalAppointment = appointments.find((a)=>a.id === extendedProps.id);\n        }\n        // Se ainda não encontrar, usar os dados do evento diretamente\n        if (!originalAppointment) {\n            var _extendedProps_insurance;\n            console.error(\"[EVENT-CLICK] Agendamento não encontrado nos dados carregados. ID:\", event.id);\n            console.log(\"[EVENT-CLICK] Usando dados do evento diretamente\");\n            // Extrair dados diretamente do evento do calendário\n            const fallbackAppointment = {\n                id: event.id,\n                title: event.title,\n                description: extendedProps.description || \"\",\n                providerId: extendedProps.providerId || \"\",\n                personId: extendedProps.personId || \"\",\n                locationId: extendedProps.locationId || \"\",\n                serviceTypeId: extendedProps.serviceTypeId || \"\",\n                insuranceId: extendedProps.insuranceId || ((_extendedProps_insurance = extendedProps.insurance) === null || _extendedProps_insurance === void 0 ? void 0 : _extendedProps_insurance.id) || \"\",\n                startDate: event.start ? event.start.toISOString() : new Date().toISOString(),\n                endDate: event.end ? event.end.toISOString() : new Date().toISOString(),\n                status: extendedProps.status || \"PENDING\",\n                // Adicionar objetos completos para uso no modal\n                insurance: extendedProps.insurance || null,\n                serviceType: extendedProps.serviceType || null,\n                person: extendedProps.person || null,\n                location: extendedProps.location || null,\n                // Adicionar extendedProps completo para debug\n                extendedProps: extendedProps\n            };\n            console.log(\"[EVENT-CLICK] Dados do evento usados como fallback:\", fallbackAppointment);\n            setSelectedAppointment(fallbackAppointment);\n            setIsModalOpen(true);\n            return;\n        }\n        console.log(\"[EVENT-CLICK] Agendamento original encontrado:\", originalAppointment);\n        // Extrair o personId do array Person se existir\n        const personId = ((_originalAppointment_Person = originalAppointment.Person) === null || _originalAppointment_Person === void 0 ? void 0 : (_originalAppointment_Person_ = _originalAppointment_Person[0]) === null || _originalAppointment_Person_ === void 0 ? void 0 : _originalAppointment_Person_.id) || ((_originalAppointment_person = originalAppointment.person) === null || _originalAppointment_person === void 0 ? void 0 : _originalAppointment_person.id) || originalAppointment.personId || originalAppointment.clientId || extendedProps.personId || \"\";\n        // Criar o objeto de agendamento combinando dados do original e do evento\n        const appointment = {\n            id: originalAppointment.id || event.id,\n            title: originalAppointment.title || event.title || \"\",\n            description: originalAppointment.description || extendedProps.description || \"\",\n            providerId: originalAppointment.userId || originalAppointment.providerId || extendedProps.providerId || \"\",\n            personId: personId,\n            locationId: originalAppointment.locationId || extendedProps.locationId || \"\",\n            serviceTypeId: originalAppointment.serviceTypeId || extendedProps.serviceTypeId || \"\",\n            insuranceId: originalAppointment.insuranceId || extendedProps.insuranceId || \"\",\n            startDate: originalAppointment.startDate || (event.start ? event.start.toISOString() : new Date().toISOString()),\n            endDate: originalAppointment.endDate || (event.end ? event.end.toISOString() : new Date().toISOString()),\n            status: originalAppointment.status || extendedProps.status || \"PENDING\",\n            // Adicionar objetos completos para uso no modal\n            insurance: originalAppointment.insurance || extendedProps.insurance || null,\n            serviceType: originalAppointment.serviceType || extendedProps.serviceType || null,\n            person: ((_originalAppointment_Person1 = originalAppointment.Person) === null || _originalAppointment_Person1 === void 0 ? void 0 : _originalAppointment_Person1[0]) || originalAppointment.person || extendedProps.person || null,\n            location: originalAppointment.location || extendedProps.location || null,\n            // Adicionar extendedProps completo para debug\n            extendedProps: extendedProps\n        };\n        // Log detalhado para depuração\n        console.log(\"[EVENT-CLICK] Dados do personId:\", {\n            fromPerson: (_originalAppointment_Person2 = originalAppointment.Person) === null || _originalAppointment_Person2 === void 0 ? void 0 : (_originalAppointment_Person_1 = _originalAppointment_Person2[0]) === null || _originalAppointment_Person_1 === void 0 ? void 0 : _originalAppointment_Person_1.id,\n            fromPersonObj: (_originalAppointment_person1 = originalAppointment.person) === null || _originalAppointment_person1 === void 0 ? void 0 : _originalAppointment_person1.id,\n            fromPersonId: originalAppointment.personId,\n            fromClientId: originalAppointment.clientId,\n            fromExtendedProps: extendedProps.personId,\n            final: appointment.personId\n        });\n        console.log(\"[EVENT-CLICK] Agendamento criado a partir dos dados existentes:\", {\n            id: appointment.id,\n            title: appointment.title,\n            personId: appointment.personId,\n            insuranceId: appointment.insuranceId,\n            serviceTypeId: appointment.serviceTypeId,\n            startDate: appointment.startDate,\n            endDate: appointment.endDate\n        });\n        // Abrir o modal de agendamento com os dados do evento\n        setSelectedAppointment(appointment);\n        setIsModalOpen(true);\n    };\n    // Registra alterações na visualização do calendário\n    const handleDatesSet = (dateInfo)=>{\n        console.log(\"[CALENDAR] datesSet:\", dateInfo.view.type);\n        setCurrentView(dateInfo.view.type);\n    };\n    // Pesquisa de agendamentos\n    const handleSearch = async (searchFilters)=>{\n        console.log(\"[SEARCH] handleSearch chamado com filtros:\", searchFilters);\n        await loadAppointments(searchFilters);\n    };\n    return {\n        calendarRef,\n        isModalOpen,\n        setIsModalOpen,\n        selectedDate,\n        setSelectedDate,\n        selectedAppointment,\n        setSelectedAppointment,\n        appointments,\n        isLoading,\n        currentView,\n        handleSlotClassNames,\n        handleDateSelect,\n        handleEventClick,\n        handleDatesSet,\n        handleSearch,\n        loadAppointments\n    };\n};\n_s(useAppointmentCalendar, \"cIYc9UBZaqeuyS0YTlwpTITuG+0=\", false, function() {\n    return [\n        _hooks_useSocket__WEBPACK_IMPORTED_MODULE_4__.useSocket\n    ];\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAppointmentCalendar);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/scheduler/calendar/hooks/useAppointmentCalendar.js\n"));

/***/ })

});