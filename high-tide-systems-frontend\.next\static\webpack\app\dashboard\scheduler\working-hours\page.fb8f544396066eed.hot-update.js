"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/scheduler/working-hours/page",{

/***/ "(app-pages-browser)/./src/hooks/useSocket.js":
/*!********************************!*\
  !*** ./src/hooks/useSocket.js ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\nvar _s = $RefreshSig$();\n\n\n// Função auxiliar para obter o token atual\nconst getCurrentToken = ()=>{\n    return localStorage.getItem('token');\n};\nfunction useSocket(url) {\n    _s();\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSocket.useEffect\": ()=>{\n            // Verificar se o usuário está autenticado\n            const token = getCurrentToken();\n            if (!token) return;\n            // Criar instância do socket\n            const socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_1__.io)(url, {\n                auth: {\n                    token\n                },\n                reconnectionAttempts: 5,\n                reconnectionDelay: 1000,\n                autoConnect: true\n            });\n            // Configurar handlers de conexão\n            socketInstance.on('connect', {\n                \"useSocket.useEffect\": ()=>{\n                    console.log('Socket conectado:', socketInstance.id);\n                }\n            }[\"useSocket.useEffect\"]);\n            socketInstance.on('connect_error', {\n                \"useSocket.useEffect\": (err)=>{\n                    console.error('Erro de conexão:', err.message);\n                }\n            }[\"useSocket.useEffect\"]);\n            socketInstance.on('disconnect', {\n                \"useSocket.useEffect\": (reason)=>{\n                    console.log('Socket desconectado:', reason);\n                }\n            }[\"useSocket.useEffect\"]);\n            setSocket(socketInstance);\n            // Limpar socket ao desmontar\n            return ({\n                \"useSocket.useEffect\": ()=>{\n                    if (socketInstance) {\n                        socketInstance.disconnect();\n                    }\n                }\n            })[\"useSocket.useEffect\"];\n        }\n    }[\"useSocket.useEffect\"], [\n        url\n    ]);\n    return socket;\n}\n_s(useSocket, \"NvwYO9vJOwIMt5STdlMKfWhuxZw=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSocket.js\n"));

/***/ })

});