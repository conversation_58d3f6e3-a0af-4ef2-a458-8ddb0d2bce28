"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/scheduler/calendar/page",{

/***/ "(app-pages-browser)/./src/hooks/useSocket.js":
/*!********************************!*\
  !*** ./src/hooks/useSocket.js ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useSocket auto */ var _s = $RefreshSig$();\n\n// Função auxiliar para obter o token atual\nconst getCurrentToken = ()=>{\n    if (true) {\n        return localStorage.getItem('token');\n    }\n    return null;\n};\nfunction useSocket(url) {\n    _s();\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSocket.useEffect\": ()=>{\n            // Verificar se estamos no lado do cliente\n            if (false) {}\n            // Verificar se o usuário está autenticado\n            const token = getCurrentToken();\n            if (!token) return;\n            // Importação dinâmica do socket.io-client para evitar problemas de SSR\n            __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_socket_io-client_build_esm_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\")).then({\n                \"useSocket.useEffect\": (param)=>{\n                    let { io } = param;\n                    // Criar instância do socket\n                    const socketInstance = io(url, {\n                        auth: {\n                            token\n                        },\n                        reconnectionAttempts: 5,\n                        reconnectionDelay: 1000,\n                        autoConnect: true\n                    });\n                    // Configurar handlers de conexão\n                    socketInstance.on('connect', {\n                        \"useSocket.useEffect\": ()=>{\n                            console.log('[SOCKET] Conectado:', socketInstance.id);\n                        }\n                    }[\"useSocket.useEffect\"]);\n                    socketInstance.on('connect_error', {\n                        \"useSocket.useEffect\": (err)=>{\n                            console.error('[SOCKET] Erro de conexão:', err.message);\n                        }\n                    }[\"useSocket.useEffect\"]);\n                    socketInstance.on('disconnect', {\n                        \"useSocket.useEffect\": (reason)=>{\n                            console.log('[SOCKET] Desconectado:', reason);\n                        }\n                    }[\"useSocket.useEffect\"]);\n                    setSocket(socketInstance);\n                }\n            }[\"useSocket.useEffect\"]).catch({\n                \"useSocket.useEffect\": (error)=>{\n                    console.error('[SOCKET] Erro ao importar socket.io-client:', error);\n                }\n            }[\"useSocket.useEffect\"]);\n            // Limpar socket ao desmontar\n            return ({\n                \"useSocket.useEffect\": ()=>{\n                    if (socket) {\n                        socket.disconnect();\n                        setSocket(null);\n                    }\n                }\n            })[\"useSocket.useEffect\"];\n        }\n    }[\"useSocket.useEffect\"], [\n        url\n    ]);\n    return socket;\n}\n_s(useSocket, \"NvwYO9vJOwIMt5STdlMKfWhuxZw=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSocket.js\n"));

/***/ })

});