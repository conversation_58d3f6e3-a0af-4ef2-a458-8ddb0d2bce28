"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/modules/scheduler/calendar/hooks/useAppointmentCalendar.js":
/*!****************************************************************************!*\
  !*** ./src/app/modules/scheduler/calendar/hooks/useAppointmentCalendar.js ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/modules/scheduler/services/appointmentService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/appointmentService.js\");\n/* harmony import */ var _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/appointmentConstants */ \"(app-pages-browser)/./src/app/modules/scheduler/calendar/utils/appointmentConstants.js\");\n/* harmony import */ var _utils_dateFormatters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/dateFormatters */ \"(app-pages-browser)/./src/utils/dateFormatters.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\n\nconst useAppointmentCalendar = (filters, isDarkMode, permissions)=>{\n    _s();\n    const calendarRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [selectedAppointment, setSelectedAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"dayGridMonth\");\n    // Extrair permissões\n    const { canCreateAppointment, canEditAppointment, canDeleteAppointment } = permissions;\n    // Socket.IO para atualizações em tempo real\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Inicializar Socket.IO apenas no cliente\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAppointmentCalendar.useEffect\": ()=>{\n            if (false) {}\n            const initSocket = {\n                \"useAppointmentCalendar.useEffect.initSocket\": async ()=>{\n                    try {\n                        const token = localStorage.getItem('token');\n                        if (!token) return;\n                        // Importação dinâmica para evitar problemas de SSR\n                        const { default: io } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_socket_io-client_build_esm_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\"));\n                        const socketInstance = io(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000', {\n                            auth: {\n                                token\n                            },\n                            reconnectionAttempts: 5,\n                            reconnectionDelay: 1000,\n                            autoConnect: true\n                        });\n                        socketInstance.on('connect', {\n                            \"useAppointmentCalendar.useEffect.initSocket\": ()=>{\n                                console.log('[CALENDAR-SOCKET] Conectado:', socketInstance.id);\n                            }\n                        }[\"useAppointmentCalendar.useEffect.initSocket\"]);\n                        socketInstance.on('connect_error', {\n                            \"useAppointmentCalendar.useEffect.initSocket\": (err)=>{\n                                console.error('[CALENDAR-SOCKET] Erro de conexão:', err.message);\n                            }\n                        }[\"useAppointmentCalendar.useEffect.initSocket\"]);\n                        socketInstance.on('disconnect', {\n                            \"useAppointmentCalendar.useEffect.initSocket\": (reason)=>{\n                                console.log('[CALENDAR-SOCKET] Desconectado:', reason);\n                            }\n                        }[\"useAppointmentCalendar.useEffect.initSocket\"]);\n                        setSocket(socketInstance);\n                    } catch (error) {\n                        console.error('[CALENDAR-SOCKET] Erro ao inicializar socket:', error);\n                    }\n                }\n            }[\"useAppointmentCalendar.useEffect.initSocket\"];\n            initSocket();\n            // Cleanup\n            return ({\n                \"useAppointmentCalendar.useEffect\": ()=>{\n                    if (socket) {\n                        socket.disconnect();\n                        setSocket(null);\n                    }\n                }\n            })[\"useAppointmentCalendar.useEffect\"];\n        }\n    }[\"useAppointmentCalendar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAppointmentCalendar.useEffect\": ()=>{\n            loadAppointments();\n        }\n    }[\"useAppointmentCalendar.useEffect\"], []);\n    // Socket.IO listeners para atualizações em tempo real\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAppointmentCalendar.useEffect\": ()=>{\n            if (!socket || typeof socket.on !== 'function') {\n                console.log('[CALENDAR-SOCKET] Socket não disponível ou inválido');\n                return;\n            }\n            console.log('[CALENDAR-SOCKET] Configurando listeners Socket.IO');\n            // Listener para agendamentos criados\n            const handleAppointmentCreated = {\n                \"useAppointmentCalendar.useEffect.handleAppointmentCreated\": (data)=>{\n                    console.log('[CALENDAR-SOCKET] Agendamento(s) criado(s):', data);\n                    // Recarregar agendamentos para garantir que temos os dados mais atuais\n                    loadAppointments();\n                }\n            }[\"useAppointmentCalendar.useEffect.handleAppointmentCreated\"];\n            // Listener para agendamentos atualizados\n            const handleAppointmentUpdated = {\n                \"useAppointmentCalendar.useEffect.handleAppointmentUpdated\": (data)=>{\n                    console.log('[CALENDAR-SOCKET] Agendamento atualizado:', data);\n                    // Recarregar agendamentos para garantir que temos os dados mais atuais\n                    loadAppointments();\n                }\n            }[\"useAppointmentCalendar.useEffect.handleAppointmentUpdated\"];\n            // Listener para agendamentos deletados\n            const handleAppointmentDeleted = {\n                \"useAppointmentCalendar.useEffect.handleAppointmentDeleted\": (data)=>{\n                    console.log('[CALENDAR-SOCKET] Agendamento deletado:', data);\n                    // Recarregar agendamentos para garantir que temos os dados mais atuais\n                    loadAppointments();\n                }\n            }[\"useAppointmentCalendar.useEffect.handleAppointmentDeleted\"];\n            // Registrar listeners\n            socket.on('appointment:created', handleAppointmentCreated);\n            socket.on('appointment:updated', handleAppointmentUpdated);\n            socket.on('appointment:deleted', handleAppointmentDeleted);\n            // Cleanup listeners\n            return ({\n                \"useAppointmentCalendar.useEffect\": ()=>{\n                    console.log('[CALENDAR-SOCKET] Removendo listeners Socket.IO');\n                    if (socket && typeof socket.off === 'function') {\n                        socket.off('appointment:created', handleAppointmentCreated);\n                        socket.off('appointment:updated', handleAppointmentUpdated);\n                        socket.off('appointment:deleted', handleAppointmentDeleted);\n                    }\n                }\n            })[\"useAppointmentCalendar.useEffect\"];\n        }\n    }[\"useAppointmentCalendar.useEffect\"], [\n        socket\n    ]);\n    // Recarregar os agendamentos quando o modo dark muda para ajustar as cores\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAppointmentCalendar.useEffect\": ()=>{\n            if (appointments.length > 0) {\n                const updatedAppointments = appointments.map({\n                    \"useAppointmentCalendar.useEffect.updatedAppointments\": (appointment)=>{\n                        var _APPOINTMENT_STATUS_, _APPOINTMENT_STATUS_1, _APPOINTMENT_STATUS_2, _APPOINTMENT_STATUS_3;\n                        return {\n                            ...appointment,\n                            backgroundColor: isDarkMode ? (_APPOINTMENT_STATUS_ = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_ === void 0 ? void 0 : _APPOINTMENT_STATUS_.darkColor : (_APPOINTMENT_STATUS_1 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_1 === void 0 ? void 0 : _APPOINTMENT_STATUS_1.color,\n                            borderColor: isDarkMode ? (_APPOINTMENT_STATUS_2 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_2 === void 0 ? void 0 : _APPOINTMENT_STATUS_2.darkColor : (_APPOINTMENT_STATUS_3 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[appointment.extendedProps.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_3 === void 0 ? void 0 : _APPOINTMENT_STATUS_3.color\n                        };\n                    }\n                }[\"useAppointmentCalendar.useEffect.updatedAppointments\"]);\n                setAppointments(updatedAppointments);\n            }\n        }\n    }[\"useAppointmentCalendar.useEffect\"], [\n        isDarkMode\n    ]);\n    const loadAppointments = async function() {\n        let searchFilters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : filters;\n        setIsLoading(true);\n        try {\n            // Para clientes, não enviamos o filtro de providers (profissionais)\n            const clientSafeFilters = {\n                ...searchFilters\n            };\n            // Se o usuário for cliente, remover o filtro de providers\n            if (clientSafeFilters.providers && clientSafeFilters.providers.length > 0) {\n                console.log(\"[CLIENT-FILTER] Removendo filtro de providers para cliente\");\n                delete clientSafeFilters.providers;\n            }\n            const response = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_1__.appointmentService.getAppointments(clientSafeFilters);\n            if (!(response === null || response === void 0 ? void 0 : response.schedulings) && !(response === null || response === void 0 ? void 0 : response.appointments)) {\n                console.error(\"Resposta inválida da API:\", response);\n                setAppointments([]);\n                return;\n            }\n            const schedulings = response.appointments || [];\n            console.log(\"[APPOINTMENTS] Carregados \".concat(schedulings.length, \" agendamentos\"));\n            const formattedAppointments = schedulings.map((scheduling)=>{\n                var _APPOINTMENT_STATUS_, _APPOINTMENT_STATUS_1, _APPOINTMENT_STATUS_2, _APPOINTMENT_STATUS_3, _scheduling_Person_, _scheduling_Person, _scheduling_provider, _scheduling_Person_1, _scheduling_Person1, _scheduling_serviceType, _scheduling_insurance, _scheduling_Person2;\n                // Usar as datas exatamente como estão no banco de dados\n                // Não aplicar nenhuma conversão de fuso horário\n                console.log(\"[APPOINTMENT] \".concat(scheduling.title, \" - Original: \").concat(scheduling.startDate));\n                return {\n                    id: scheduling.id,\n                    title: scheduling.title || \"\",\n                    start: scheduling.startDate,\n                    end: scheduling.endDate,\n                    backgroundColor: isDarkMode ? (_APPOINTMENT_STATUS_ = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_ === void 0 ? void 0 : _APPOINTMENT_STATUS_.darkColor : (_APPOINTMENT_STATUS_1 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_1 === void 0 ? void 0 : _APPOINTMENT_STATUS_1.color,\n                    borderColor: isDarkMode ? (_APPOINTMENT_STATUS_2 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_2 === void 0 ? void 0 : _APPOINTMENT_STATUS_2.darkColor : (_APPOINTMENT_STATUS_3 = _utils_appointmentConstants__WEBPACK_IMPORTED_MODULE_2__.APPOINTMENT_STATUS[scheduling.status || \"PENDING\"]) === null || _APPOINTMENT_STATUS_3 === void 0 ? void 0 : _APPOINTMENT_STATUS_3.color,\n                    extendedProps: {\n                        description: scheduling.description || \"\",\n                        personfullName: ((_scheduling_Person = scheduling.Person) === null || _scheduling_Person === void 0 ? void 0 : (_scheduling_Person_ = _scheduling_Person[0]) === null || _scheduling_Person_ === void 0 ? void 0 : _scheduling_Person_.fullName) || scheduling.personfullName || \"\",\n                        providerfullName: ((_scheduling_provider = scheduling.provider) === null || _scheduling_provider === void 0 ? void 0 : _scheduling_provider.fullName) || scheduling.providerfullName || \"\",\n                        providerId: scheduling.userId || scheduling.providerId || \"\",\n                        // Garantir que o personId seja definido corretamente\n                        personId: ((_scheduling_Person1 = scheduling.Person) === null || _scheduling_Person1 === void 0 ? void 0 : (_scheduling_Person_1 = _scheduling_Person1[0]) === null || _scheduling_Person_1 === void 0 ? void 0 : _scheduling_Person_1.id) || scheduling.personId || scheduling.clientId || \"\",\n                        locationId: scheduling.locationId || \"\",\n                        serviceTypefullName: ((_scheduling_serviceType = scheduling.serviceType) === null || _scheduling_serviceType === void 0 ? void 0 : _scheduling_serviceType.name) || scheduling.serviceTypefullName || \"\",\n                        serviceTypeId: scheduling.serviceTypeId || \"\",\n                        status: scheduling.status || \"PENDING\",\n                        // Adicionar informações de convênio, se disponíveis\n                        insurance: scheduling.insurance || {},\n                        insuranceId: scheduling.insuranceId || ((_scheduling_insurance = scheduling.insurance) === null || _scheduling_insurance === void 0 ? void 0 : _scheduling_insurance.id) || \"\",\n                        insuranceInfo: scheduling.insuranceInfo || {},\n                        // Adicionar objetos completos para uso no modal\n                        person: ((_scheduling_Person2 = scheduling.Person) === null || _scheduling_Person2 === void 0 ? void 0 : _scheduling_Person2[0]) || scheduling.person || null,\n                        serviceType: scheduling.serviceType || null\n                    }\n                };\n            });\n            setAppointments(formattedAppointments);\n        } catch (error) {\n            console.error(\"Erro ao carregar agendamentos:\", error);\n            setAppointments([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Função para lidar com slots do calendário\n    const handleSlotClassNames = (slotInfo)=>{\n        // Só aplicamos essa lógica nas visualizações de semana e dia\n        if (currentView !== \"timeGridWeek\" && currentView !== \"timeGridDay\") {\n            return [\n                \"min-h-[120px] p-1\"\n            ];\n        }\n        return [\n            \"min-h-[120px] p-1\"\n        ];\n    };\n    // Função para lidar com a seleção de datas\n    const handleDateSelect = async (selectInfo, checkProvidersAvailability, setErrorMessage)=>{\n        var _selectInfo_end;\n        // Verificar permissão para criar agendamentos\n        if (!canCreateAppointment) {\n            setErrorMessage(\"Você não tem permissão para criar novos agendamentos.\");\n            return;\n        }\n        console.log(\"[DEBUG-SELECT] selectInfo recebido (horário local):\", {\n            start: selectInfo.start.toLocaleString(),\n            end: (_selectInfo_end = selectInfo.end) === null || _selectInfo_end === void 0 ? void 0 : _selectInfo_end.toLocaleString(),\n            dia: selectInfo.start.getDay(),\n            hora: selectInfo.start.getHours(),\n            view: currentView\n        });\n        // Usar os objetos Date diretamente, sem conversão para strings ISO\n        // Isso mantém o horário local que o usuário selecionou\n        console.log(\"[DEBUG-DURATION] Antes de calcular end time - selectInfo.end existe?\", !!selectInfo.end);\n        console.log(\"[DEBUG-DURATION] FORÇANDO duração padrão de 60 minutos, ignorando o valor de end\");\n        // Sempre usar 1 hora, independentemente do valor de end que o FullCalendar fornece\n        const endTime = new Date(selectInfo.start.getTime() + 60 * 60 * 1000);\n        console.log(\"[DEBUG-DURATION] End time forçado para 1 hora:\", endTime.toLocaleString());\n        console.log(\"[DEBUG-DURATION] Duração forçada (minutos):\", (endTime - selectInfo.start) / (60 * 1000));\n        const selectInfoLocal = {\n            ...selectInfo,\n            // Garantir que temos objetos Date válidos\n            start: selectInfo.start,\n            end: endTime\n        };\n        console.log(\"[DEBUG-DURATION] selectInfoLocal final:\", {\n            start: selectInfoLocal.start.toLocaleString(),\n            end: selectInfoLocal.end.toLocaleString(),\n            duracaoMinutos: (selectInfoLocal.end - selectInfoLocal.start) / (60 * 1000)\n        });\n        // Limpa mensagens de erro anteriores\n        setErrorMessage(null);\n        // Verificação específica para visualizações semanal e diária\n        if ((currentView === \"timeGridWeek\" || currentView === \"timeGridDay\") && filters.providers && filters.providers.length > 0) {\n            console.log(\"[SELECT] Realizando verificação de disponibilidade\");\n            // Verifica se algum provider está disponível no horário\n            const isAnyProviderAvailable = await checkProvidersAvailability(selectInfoLocal);\n            console.log(\"[SELECT] Resultado da verificação:\", isAnyProviderAvailable ? \"Disponível\" : \"Indisponível\");\n            if (!isAnyProviderAvailable) {\n                const errorMsg = \"Nenhum dos profissionais selecionados est\\xe1 dispon\\xedvel no hor\\xe1rio escolhido.\\n        Por favor, verifique o quadro de hor\\xe1rios ou selecione outro hor\\xe1rio.\";\n                console.log(\"[SELECT] Exibindo mensagem de erro:\", errorMsg);\n                setErrorMessage(errorMsg);\n                return;\n            }\n        }\n        console.log(\"[SELECT] Abrindo modal para criar novo agendamento\");\n        setSelectedDate(selectInfoLocal);\n        setSelectedAppointment(null);\n        setIsModalOpen(true);\n    };\n    // Função para lidar com cliques em eventos\n    const handleEventClick = async (clickInfo, checkSingleProviderAvailability, setErrorMessage)=>{\n        var _clickInfo_event_start, _clickInfo_event_end, _clickInfo_event_extendedProps, _filters_providers, _originalAppointment_Person_, _originalAppointment_Person, _originalAppointment_person, _originalAppointment_Person1, _originalAppointment_Person_1, _originalAppointment_Person2, _originalAppointment_person1;\n        // Verificar permissão para editar agendamentos\n        if (!canEditAppointment) {\n            // Para clientes, apenas mostrar os detalhes sem permitir edição\n            const event = clickInfo.event;\n            const extendedProps = event.extendedProps || {};\n            // Selecionar o agendamento para visualização (sem edição)\n            setSelectedAppointment({\n                id: event.id,\n                title: event.title,\n                startDate: event.start,\n                endDate: event.end,\n                ...extendedProps\n            });\n            // Abrir o modal (a verificação de permissão para edição será feita no modal)\n            setIsModalOpen(true);\n            return;\n        }\n        console.log(\"[EVENT-CLICK] handleEventClick chamado:\", {\n            view: currentView,\n            eventId: clickInfo.event.id,\n            eventTitle: clickInfo.event.title,\n            eventStart: (_clickInfo_event_start = clickInfo.event.start) === null || _clickInfo_event_start === void 0 ? void 0 : _clickInfo_event_start.toLocaleString(),\n            eventEnd: (_clickInfo_event_end = clickInfo.event.end) === null || _clickInfo_event_end === void 0 ? void 0 : _clickInfo_event_end.toLocaleString(),\n            providerId: (_clickInfo_event_extendedProps = clickInfo.event.extendedProps) === null || _clickInfo_event_extendedProps === void 0 ? void 0 : _clickInfo_event_extendedProps.providerId,\n            hasProviders: ((_filters_providers = filters.providers) === null || _filters_providers === void 0 ? void 0 : _filters_providers.length) > 0\n        });\n        const event = clickInfo.event;\n        const extendedProps = event.extendedProps || {};\n        const providerId = extendedProps.providerId;\n        // Verificação de disponibilidade na visualização semanal/diária\n        if ((currentView === \"timeGridWeek\" || currentView === \"timeGridDay\") && filters.providers && filters.providers.length > 0 && providerId) {\n            console.log(\"[EVENT-CLICK] Realizando verificação para evento clicado\");\n            // Verifica se o providerId do evento está entre os filtrados\n            const isProviderInFilters = filters.providers.includes(providerId);\n            console.log(\"[EVENT-CLICK] Provider est\\xe1 nos filtros? \".concat(isProviderInFilters));\n            if (isProviderInFilters) {\n                // Cria um objeto com formato similar ao selectInfo\n                // Usar os objetos Date diretamente, sem conversão para strings ISO\n                // SEMPRE usar duração de 1 hora, ignorando o valor de end\n                console.log(\"[EVENT-CLICK-DEBUG] FORÇANDO duração de 60 minutos para o evento\");\n                const dateInfo = {\n                    start: event.start,\n                    end: new Date(event.start.getTime() + 60 * 60 * 1000)\n                };\n                console.log(\"[EVENT-CLICK-DEBUG] Duração forçada (minutos):\", (dateInfo.end - dateInfo.start) / (60 * 1000));\n                // Verifica a disponibilidade\n                console.log(\"[EVENT-CLICK] Verificando disponibilidade para o evento\");\n                const isAvailable = await checkSingleProviderAvailability(providerId, dateInfo);\n                console.log(\"[EVENT-CLICK] Evento est\\xe1 dentro do hor\\xe1rio de trabalho? \".concat(isAvailable));\n                if (!isAvailable) {\n                    const errorMsg = \"Este hor\\xe1rio est\\xe1 fora do per\\xedodo de trabalho do profissional selecionado.\\n          Por favor, verifique o quadro de hor\\xe1rios ou selecione outro hor\\xe1rio.\";\n                    console.log(\"[EVENT-CLICK] Exibindo mensagem de erro:\", errorMsg);\n                    setErrorMessage(errorMsg);\n                    return;\n                }\n            }\n        }\n        console.log(\"[EVENT-CLICK] Abrindo modal para editar agendamento existente\");\n        // Encontrar o agendamento original nos dados carregados\n        // Primeiro, procurar pelo ID exato\n        let originalAppointment = appointments.find((a)=>a.id === event.id);\n        // Se não encontrar, tentar buscar pelo ID no extendedProps\n        if (!originalAppointment && extendedProps.id) {\n            originalAppointment = appointments.find((a)=>a.id === extendedProps.id);\n        }\n        // Se ainda não encontrar, usar os dados do evento diretamente\n        if (!originalAppointment) {\n            var _extendedProps_insurance;\n            console.error(\"[EVENT-CLICK] Agendamento não encontrado nos dados carregados. ID:\", event.id);\n            console.log(\"[EVENT-CLICK] Usando dados do evento diretamente\");\n            // Extrair dados diretamente do evento do calendário\n            const fallbackAppointment = {\n                id: event.id,\n                title: event.title,\n                description: extendedProps.description || \"\",\n                providerId: extendedProps.providerId || \"\",\n                personId: extendedProps.personId || \"\",\n                locationId: extendedProps.locationId || \"\",\n                serviceTypeId: extendedProps.serviceTypeId || \"\",\n                insuranceId: extendedProps.insuranceId || ((_extendedProps_insurance = extendedProps.insurance) === null || _extendedProps_insurance === void 0 ? void 0 : _extendedProps_insurance.id) || \"\",\n                startDate: event.start ? event.start.toISOString() : new Date().toISOString(),\n                endDate: event.end ? event.end.toISOString() : new Date().toISOString(),\n                status: extendedProps.status || \"PENDING\",\n                // Adicionar objetos completos para uso no modal\n                insurance: extendedProps.insurance || null,\n                serviceType: extendedProps.serviceType || null,\n                person: extendedProps.person || null,\n                location: extendedProps.location || null,\n                // Adicionar extendedProps completo para debug\n                extendedProps: extendedProps\n            };\n            console.log(\"[EVENT-CLICK] Dados do evento usados como fallback:\", fallbackAppointment);\n            setSelectedAppointment(fallbackAppointment);\n            setIsModalOpen(true);\n            return;\n        }\n        console.log(\"[EVENT-CLICK] Agendamento original encontrado:\", originalAppointment);\n        // Extrair o personId do array Person se existir\n        const personId = ((_originalAppointment_Person = originalAppointment.Person) === null || _originalAppointment_Person === void 0 ? void 0 : (_originalAppointment_Person_ = _originalAppointment_Person[0]) === null || _originalAppointment_Person_ === void 0 ? void 0 : _originalAppointment_Person_.id) || ((_originalAppointment_person = originalAppointment.person) === null || _originalAppointment_person === void 0 ? void 0 : _originalAppointment_person.id) || originalAppointment.personId || originalAppointment.clientId || extendedProps.personId || \"\";\n        // Criar o objeto de agendamento combinando dados do original e do evento\n        const appointment = {\n            id: originalAppointment.id || event.id,\n            title: originalAppointment.title || event.title || \"\",\n            description: originalAppointment.description || extendedProps.description || \"\",\n            providerId: originalAppointment.userId || originalAppointment.providerId || extendedProps.providerId || \"\",\n            personId: personId,\n            locationId: originalAppointment.locationId || extendedProps.locationId || \"\",\n            serviceTypeId: originalAppointment.serviceTypeId || extendedProps.serviceTypeId || \"\",\n            insuranceId: originalAppointment.insuranceId || extendedProps.insuranceId || \"\",\n            startDate: originalAppointment.startDate || (event.start ? event.start.toISOString() : new Date().toISOString()),\n            endDate: originalAppointment.endDate || (event.end ? event.end.toISOString() : new Date().toISOString()),\n            status: originalAppointment.status || extendedProps.status || \"PENDING\",\n            // Adicionar objetos completos para uso no modal\n            insurance: originalAppointment.insurance || extendedProps.insurance || null,\n            serviceType: originalAppointment.serviceType || extendedProps.serviceType || null,\n            person: ((_originalAppointment_Person1 = originalAppointment.Person) === null || _originalAppointment_Person1 === void 0 ? void 0 : _originalAppointment_Person1[0]) || originalAppointment.person || extendedProps.person || null,\n            location: originalAppointment.location || extendedProps.location || null,\n            // Adicionar extendedProps completo para debug\n            extendedProps: extendedProps\n        };\n        // Log detalhado para depuração\n        console.log(\"[EVENT-CLICK] Dados do personId:\", {\n            fromPerson: (_originalAppointment_Person2 = originalAppointment.Person) === null || _originalAppointment_Person2 === void 0 ? void 0 : (_originalAppointment_Person_1 = _originalAppointment_Person2[0]) === null || _originalAppointment_Person_1 === void 0 ? void 0 : _originalAppointment_Person_1.id,\n            fromPersonObj: (_originalAppointment_person1 = originalAppointment.person) === null || _originalAppointment_person1 === void 0 ? void 0 : _originalAppointment_person1.id,\n            fromPersonId: originalAppointment.personId,\n            fromClientId: originalAppointment.clientId,\n            fromExtendedProps: extendedProps.personId,\n            final: appointment.personId\n        });\n        console.log(\"[EVENT-CLICK] Agendamento criado a partir dos dados existentes:\", {\n            id: appointment.id,\n            title: appointment.title,\n            personId: appointment.personId,\n            insuranceId: appointment.insuranceId,\n            serviceTypeId: appointment.serviceTypeId,\n            startDate: appointment.startDate,\n            endDate: appointment.endDate\n        });\n        // Abrir o modal de agendamento com os dados do evento\n        setSelectedAppointment(appointment);\n        setIsModalOpen(true);\n    };\n    // Registra alterações na visualização do calendário\n    const handleDatesSet = (dateInfo)=>{\n        console.log(\"[CALENDAR] datesSet:\", dateInfo.view.type);\n        setCurrentView(dateInfo.view.type);\n    };\n    // Pesquisa de agendamentos\n    const handleSearch = async (searchFilters)=>{\n        console.log(\"[SEARCH] handleSearch chamado com filtros:\", searchFilters);\n        await loadAppointments(searchFilters);\n    };\n    return {\n        calendarRef,\n        isModalOpen,\n        setIsModalOpen,\n        selectedDate,\n        setSelectedDate,\n        selectedAppointment,\n        setSelectedAppointment,\n        appointments,\n        isLoading,\n        currentView,\n        handleSlotClassNames,\n        handleDateSelect,\n        handleEventClick,\n        handleDatesSet,\n        handleSearch,\n        loadAppointments\n    };\n};\n_s(useAppointmentCalendar, \"2cL1DwK4B71ANIrItpMj+ACfQio=\");\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAppointmentCalendar);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/scheduler/calendar/hooks/useAppointmentCalendar.js\n"));

/***/ })

});